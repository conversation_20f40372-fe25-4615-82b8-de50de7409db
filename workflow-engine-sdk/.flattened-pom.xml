<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.winit</groupId>
  <artifactId>workflow-engine-sdk</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <dependencies>
    <dependency>
      <groupId>com.winit</groupId>
      <artifactId>workflow-engine-core</artifactId>
      <version>1.0.0-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.winit</groupId>
      <artifactId>workflow-engine-provider</artifactId>
      <version>1.0.0-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <version>1.2.11</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.winit</groupId>
      <artifactId>workflow-openapi</artifactId>
      <version>1.0.0-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.38</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.mvel</groupId>
      <artifactId>mvel2</artifactId>
      <version>2.5.2.Final</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
</project>
