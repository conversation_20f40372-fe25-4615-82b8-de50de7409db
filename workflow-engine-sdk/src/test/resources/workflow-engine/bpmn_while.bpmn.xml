<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_15tmk22" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.0.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.15.0">
  <bpmn:process id="Process_1uyi5fg" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:outgoing>Flow_0b1u4b1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0b1u4b1" sourceRef="StartEvent_1" targetRef="Gateway_1y5v7q8" />
    <bpmn:exclusiveGateway id="Gateway_1y5v7q8">
      <bpmn:incoming>Flow_0b1u4b1</bpmn:incoming>
      <bpmn:incoming>Flow_0h5lpsw</bpmn:incoming>
      <bpmn:outgoing>Flow_1hf2k4h</bpmn:outgoing>
      <bpmn:outgoing>Flow_1m127xt</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1hf2k4h" sourceRef="Gateway_1y5v7q8" targetRef="Event_0izeb0g">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${cycle_index &gt;= cycle_count}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0h5lpsw" sourceRef="Activity_1ohhd5r" targetRef="Gateway_1y5v7q8">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${cycle_index += 1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_0izeb0g" name="结束">
      <bpmn:incoming>Flow_1hf2k4h</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="Activity_1ohhd5r" name="百度搜索关键词" camunda:class="com.winit.workflow.engine.sdk.delegation.EventProcessorJavaDelegation">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="baseUrl" value="https://www.baidu.com" />
          <camunda:property name="url" value="${baseUrl + &#39;/api/endpoint&#39;}" />
          <camunda:property name="timestamp" value="${now()}" />
          <camunda:property name="greeting" value="${concat(&#39;Hello, &#39;, user.name,generateOrderNumber())}" />
          <camunda:property name="order" value="${generateOrderNumber()}" />
          <camunda:property name="aList" value="[${now()},${now()},${generateOrderNumber()}]" />
          <camunda:property name="aMap" value="{a1:${now()},b1:${user.name}}" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1m127xt</bpmn:incoming>
      <bpmn:outgoing>Flow_0h5lpsw</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1m127xt" sourceRef="Gateway_1y5v7q8" targetRef="Activity_1ohhd5r">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${cycle_index &lt; cycle_count}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1uyi5fg">
      <bpmndi:BPMNEdge id="Flow_1m127xt_di" bpmnElement="Flow_1m127xt">
        <di:waypoint x="385" y="350" />
        <di:waypoint x="700" y="350" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0h5lpsw_di" bpmnElement="Flow_0h5lpsw">
        <di:waypoint x="750" y="310" />
        <di:waypoint x="750" y="120" />
        <di:waypoint x="360" y="120" />
        <di:waypoint x="360" y="325" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hf2k4h_di" bpmnElement="Flow_1hf2k4h">
        <di:waypoint x="360" y="375" />
        <di:waypoint x="360" y="470" />
        <di:waypoint x="732" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0b1u4b1_di" bpmnElement="Flow_0b1u4b1">
        <di:waypoint x="188" y="350" />
        <di:waypoint x="335" y="350" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="332" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="159" y="375" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0adwo74_di" bpmnElement="Gateway_1y5v7q8" isMarkerVisible="true">
        <dc:Bounds x="335" y="325" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0izeb0g_di" bpmnElement="Event_0izeb0g">
        <dc:Bounds x="732" y="452" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="739" y="495" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0whzy98_di" bpmnElement="Activity_1ohhd5r">
        <dc:Bounds x="700" y="310" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
