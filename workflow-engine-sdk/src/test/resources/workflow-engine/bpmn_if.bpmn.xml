<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0rwfiw2" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.0.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.15.0">
  <bpmn:process id="Process_1qanlxp" name="ICC获取余额" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始" camunda:formKey="">
      <bpmn:outgoing>Flow_1cry49q</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_1cry49q" sourceRef="StartEvent_1" targetRef="Activity_0vewrvt" />
    <bpmn:serviceTask id="Activity_0vewrvt" name="open_login_page" camunda:class="com.winit.workflow.engine.sdk.delegation.EventProcessorJavaDelegation">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="url" value="https://dashboard.vanlo.com/" />
          <camunda:property name="action" value="open" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1cry49q</bpmn:incoming>
      <bpmn:outgoing>Flow_0ddvnk1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0ddvnk1" sourceRef="Activity_0vewrvt" targetRef="Activity_04xszv9" />
    <bpmn:sequenceFlow id="Flow_1ep9ywv" name="成功" sourceRef="Gateway_1lyhwt7" targetRef="Activity_1r2f9kn">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${login_info = true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Activity_1r2f9kn" name="打开余额界面">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="action" value="opne" />
          <camunda:property name="url" value="https://dashboard.vanlo.com/balance" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1ep9ywv</bpmn:incoming>
      <bpmn:outgoing>Flow_1td3r71</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0y3llt1" name="失败" sourceRef="Gateway_1lyhwt7" targetRef="Event_1tbrdn3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${login_info = false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_1tbrdn3" name="结束">
      <bpmn:incoming>Flow_0y3llt1</bpmn:incoming>
      <bpmn:incoming>Flow_0vukjqk</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1td3r71" sourceRef="Activity_1r2f9kn" targetRef="Activity_1c4a5ff" />
    <bpmn:exclusiveGateway id="Gateway_1lyhwt7" name="判断是否登录成功">
      <bpmn:incoming>Flow_0ys438j</bpmn:incoming>
      <bpmn:outgoing>Flow_1ep9ywv</bpmn:outgoing>
      <bpmn:outgoing>Flow_0y3llt1</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Activity_04xszv9" name="输入账号" camunda:class="com.winit.workflow.engine.sdk.delegation.EventProcessorJavaDelegation">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="input_value" value="<EMAIL>" />
          <camunda:property name="action" value="input" />
          <camunda:property name="rule_type" value="xpath" />
          <camunda:property name="xpath_rule" value="//input[@name=&#39;email&#39;]" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0ddvnk1</bpmn:incoming>
      <bpmn:outgoing>Flow_0xwq5xb</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0kt1655" name="点击登录" camunda:class="com.winit.workflow.engine.sdk.delegation.EventProcessorJavaDelegation">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="action" value="click" />
          <camunda:property name="rule_type" value="xpath" />
          <camunda:property name="xpath_rule" value="//span[@class=&#39;v-icon-16&#39;]" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0gsrr60</bpmn:incoming>
      <bpmn:outgoing>Flow_1xzfh4v</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1xzfh4v" sourceRef="Activity_0kt1655" targetRef="Activity_14me067" />
    <bpmn:serviceTask id="Activity_14me067" name="获取登录页信息" camunda:class="com.winit.workflow.engine.sdk.delegation.EventProcessorJavaDelegation">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="action" value="query_result" />
          <camunda:property name="rule_type" value="xpath" />
          <camunda:property name="xpath_rule" value="//div[@class=&#39;v-username&#39;]" />
          <camunda:property name="reuslt_filed" value="login_info" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1xzfh4v</bpmn:incoming>
      <bpmn:outgoing>Flow_0ys438j</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0ys438j" sourceRef="Activity_14me067" targetRef="Gateway_1lyhwt7" />
    <bpmn:serviceTask id="Activity_1c4a5ff" name="获取余额">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="action" value="query" />
          <camunda:property name="rule_type" value="xpath" />
          <camunda:property name="xpath_rule" value="//div[@class=&#39;v-current-balance-amount&#39;]" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1td3r71</bpmn:incoming>
      <bpmn:outgoing>Flow_0vukjqk</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0vukjqk" sourceRef="Activity_1c4a5ff" targetRef="Event_1tbrdn3" />
    <bpmn:sequenceFlow id="Flow_0xwq5xb" sourceRef="Activity_04xszv9" targetRef="Activity_00oktpk" />
    <bpmn:sequenceFlow id="Flow_0gsrr60" sourceRef="Activity_00oktpk" targetRef="Activity_0kt1655" />
    <bpmn:serviceTask id="Activity_00oktpk" name="输入密码">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="input_value" value="1ssUdhee" />
          <camunda:property name="action" value="input" />
          <camunda:property name="rule_type" value="xpath" />
          <camunda:property name="xpath_rule" value="//input[@name=&#39;email&#39;]" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0xwq5xb</bpmn:incoming>
      <bpmn:outgoing>Flow_0gsrr60</bpmn:outgoing>
    </bpmn:serviceTask>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1qanlxp">
      <bpmndi:BPMNEdge id="Flow_0gsrr60_di" bpmnElement="Flow_0gsrr60">
        <di:waypoint x="630" y="177" />
        <di:waypoint x="710" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xwq5xb_di" bpmnElement="Flow_0xwq5xb">
        <di:waypoint x="470" y="177" />
        <di:waypoint x="530" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vukjqk_di" bpmnElement="Flow_0vukjqk">
        <di:waypoint x="1590" y="120" />
        <di:waypoint x="1800" y="120" />
        <di:waypoint x="1800" y="182" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ys438j_di" bpmnElement="Flow_0ys438j">
        <di:waypoint x="1000" y="177" />
        <di:waypoint x="1135" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xzfh4v_di" bpmnElement="Flow_1xzfh4v">
        <di:waypoint x="810" y="177" />
        <di:waypoint x="900" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1td3r71_di" bpmnElement="Flow_1td3r71">
        <di:waypoint x="1410" y="120" />
        <di:waypoint x="1490" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0y3llt1_di" bpmnElement="Flow_0y3llt1">
        <di:waypoint x="1160" y="202" />
        <di:waypoint x="1160" y="260" />
        <di:waypoint x="1800" y="260" />
        <di:waypoint x="1800" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1190" y="273" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ep9ywv_di" bpmnElement="Flow_1ep9ywv">
        <di:waypoint x="1160" y="150" />
        <di:waypoint x="1160" y="120" />
        <di:waypoint x="1310" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1181" y="103" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ddvnk1_di" bpmnElement="Flow_0ddvnk1">
        <di:waypoint x="320" y="177" />
        <di:waypoint x="370" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1cry49q_di" bpmnElement="Flow_1cry49q">
        <di:waypoint x="188" y="177" />
        <di:waypoint x="220" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="159" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="159" y="202" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1bn1biw_di" bpmnElement="Activity_0vewrvt">
        <dc:Bounds x="220" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xvv32o_di" bpmnElement="Activity_1r2f9kn">
        <dc:Bounds x="1310" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1tbrdn3_di" bpmnElement="Event_1tbrdn3">
        <dc:Bounds x="1782" y="182" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1750.5" y="193" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1n0laxh_di" bpmnElement="Gateway_1lyhwt7" isMarkerVisible="true">
        <dc:Bounds x="1135" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1025" y="153" width="89" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_04xszv9_di" bpmnElement="Activity_04xszv9">
        <dc:Bounds x="370" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0kt1655_di" bpmnElement="Activity_0kt1655">
        <dc:Bounds x="710" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_14me067_di" bpmnElement="Activity_14me067">
        <dc:Bounds x="900" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1c4a5ff_di" bpmnElement="Activity_1c4a5ff">
        <dc:Bounds x="1490" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xuisjg_di" bpmnElement="Activity_00oktpk">
        <dc:Bounds x="530" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
