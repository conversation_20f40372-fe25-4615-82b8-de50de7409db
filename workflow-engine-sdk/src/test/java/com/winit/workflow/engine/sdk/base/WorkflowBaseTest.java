package com.winit.workflow.engine.sdk.base;

import com.alibaba.smart.framework.engine.SmartEngine;
import com.alibaba.smart.framework.engine.configuration.ProcessEngineConfiguration;
import com.alibaba.smart.framework.engine.configuration.impl.DefaultInstanceAccessor;
import com.alibaba.smart.framework.engine.configuration.impl.DefaultProcessEngineConfiguration;
import com.alibaba.smart.framework.engine.configuration.impl.DefaultSmartEngine;
import com.winit.workflow.engine.core.api.WorkflowEngine;
import com.winit.workflow.engine.core.spi.SpiProviderFactory;
import com.winit.workflow.engine.sdk.loader.FlowResourceApiLoader;
import org.junit.After;
import org.junit.Before;

public class WorkflowBaseTest {
    public FlowResourceApiLoader flowResourceApiLoader;
    public WorkflowEngine workflowEngine = SpiProviderFactory.getProvider().createEngine(null);

    @Before
    public void setUp() {
        String baseURL = "http://127.0.0.1:9091";
        int readTimeout = 1000;
        int connectTimeout = 1000;
        flowResourceApiLoader = new FlowResourceApiLoader(baseURL, readTimeout, connectTimeout);
    }

    @After
    public void tearDown() {
    }

    public static SmartEngine constructSmartEngine() {
        ProcessEngineConfiguration processEngineConfiguration = new DefaultProcessEngineConfiguration();
        processEngineConfiguration.setInstanceAccessor(new DefaultInstanceAccessor());

        SmartEngine smartEngine = new DefaultSmartEngine();
        smartEngine.init(processEngineConfiguration);

        return smartEngine;
    }

}
