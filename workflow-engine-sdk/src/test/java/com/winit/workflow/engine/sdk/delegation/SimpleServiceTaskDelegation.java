package com.winit.workflow.engine.sdk.delegation;


import com.alibaba.smart.framework.engine.context.ExecutionContext;
import com.alibaba.smart.framework.engine.delegation.JavaDelegation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * 简单的业务服务
 */
public class SimpleServiceTaskDelegation implements JavaDelegation {

    private static final Logger LOGGER = LoggerFactory.getLogger(SimpleServiceTaskDelegation.class);
    public static final String DEFAULT_SEQUENCE_FLOW = "defaultSequenceFlow";


    @Override
    public void execute(ExecutionContext executionContext) {
        LOGGER.info("Executing service task for activity: {}", executionContext.getExecutionInstance().getProcessDefinitionActivityId());
        executionContext.getResponse().put(DEFAULT_SEQUENCE_FLOW, executionContext.getExecutionInstance().getProcessDefinitionActivityId());
        executionContext.getResponse().putAll(executionContext.getRequest());
    }
}