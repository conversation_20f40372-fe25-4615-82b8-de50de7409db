package com.winit.workflow.engine.sdk.loader;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.Ignore;

public class FlowResourceApiLoaderTest {

    private FlowResourceApiLoader flowResourceApiLoader;

    @Before
    public void setUp() {
        String baseURL = "http://127.0.0.1:9091";
        int readTimeout = 1000;
        int connectTimeout = 1000;
        flowResourceApiLoader = new FlowResourceApiLoader(baseURL, readTimeout, connectTimeout);
    }

    @After
    public void tearDown() {
    }

    @Test
    @Ignore("需要外部API服务运行，跳过集成测试")
    public void testLoad() {
        FlowContentLoader flowResourcePathLoader = flowResourceApiLoader.of("ghjk-fgdhjk-fgvbnm-lkjnbv", "1.0.0");
        String load = flowResourcePathLoader.load();
        Assert.assertNotNull(load);

        FlowContentLoader flowResourcePathLoader1 = flowResourceApiLoader.of("ghjk-fgdhjkd-fgvbnm-lkjnbv", "1.0.0");
        String load1 = flowResourcePathLoader1.load();
        Assert.assertNotNull(load1);
    }
}