package com.winit.workflow.engine.sdk.vo.request;

import com.winit.workflow.engine.sdk.loader.FlowContentLoader;
import com.winit.workflow.engine.sdk.loader.FlowResourcePathLoader;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.*;

/**
 * DeployFlowRequest的单元测试
 * 使用JUnit 4框架进行测试
 */
public class DeployFlowRequestTest {

    private DeployFlowRequest deployFlowRequest;

    @Before
    public void setUp() {
        deployFlowRequest = new DeployFlowRequest();
    }

    @Test
    public void testDefaultConstructor() {
        // 测试默认构造函数
        DeployFlowRequest request = new DeployFlowRequest();

        assertNotNull(request);
        assertNull(request.getProcessDefinitionContent());
        assertNull(request.getOperator()); // 继承自BaseRequest
    }

    @Test
    public void testSetAndGetProcessDefinitionContent() {
        // 测试设置和获取流程定义内容
        String content = "test process definition content";
        deployFlowRequest.setProcessDefinitionContent(content);

        assertEquals(content, deployFlowRequest.getProcessDefinitionContent());
    }

    @Test
    public void testSetAndGetProcessDefinitionContentWithNull() {
        // 测试设置null流程定义内容
        deployFlowRequest.setProcessDefinitionContent(null);

        assertNull(deployFlowRequest.getProcessDefinitionContent());
    }

    @Test
    public void testSetAndGetOperator() {
        // 测试设置和获取操作人（继承自BaseRequest）
        String operator = "deployUser";
        deployFlowRequest.setOperator(operator);
        
        assertEquals(operator, deployFlowRequest.getOperator());
    }

    @Test
    public void testCompleteRequestSetup() {
        // 测试完整的请求设置
        String operator = "admin";
        String processName = "testProcess";
        String processContent = "test content";

        deployFlowRequest.setOperator(operator);
        deployFlowRequest.setProcessDefinitionName(processName);
        deployFlowRequest.setProcessDefinitionContent(processContent);

        assertEquals(operator, deployFlowRequest.getOperator());
        assertEquals(processName, deployFlowRequest.getProcessDefinitionName());
        assertEquals(processContent, deployFlowRequest.getProcessDefinitionContent());
    }

    @Test
    public void testSetAndGetProcessDefinitionName() {
        // 测试设置和获取流程定义名称
        String processName = "testProcessName";
        deployFlowRequest.setProcessDefinitionName(processName);

        assertEquals(processName, deployFlowRequest.getProcessDefinitionName());
    }

    @Test
    public void testInheritanceFromBaseRequest() {
        // 测试继承关系
        assertTrue(deployFlowRequest instanceof BaseRequest);

        // 测试可以调用父类方法
        deployFlowRequest.setOperator("inheritanceTest");
        assertEquals("inheritanceTest", deployFlowRequest.getOperator());
    }

    @Test
    public void testSetAndGetProcessDefinitionId() {
        // 测试设置和获取流程定义ID
        String processId = "process-123";
        deployFlowRequest.setProcessDefinitionId(processId);

        assertEquals(processId, deployFlowRequest.getProcessDefinitionId());
    }

    @Test
    public void testSetAndGetProcessDefinitionVersion() {
        // 测试设置和获取流程定义版本
        String version = "1.0.0";
        deployFlowRequest.setProcessDefinitionVersion(version);

        assertEquals(version, deployFlowRequest.getProcessDefinitionVersion());
    }

    @Test
    public void testSetAndGetDeploymentUserId() {
        // 测试设置和获取部署用户ID
        String userId = "deploy-user-123";
        deployFlowRequest.setDeploymentUserId(userId);

        assertEquals(userId, deployFlowRequest.getDeploymentUserId());
    }

    @Test
    public void testSetAndGetProcessDefinitionType() {
        // 测试设置和获取流程定义类型
        String processType = "BPMN";
        deployFlowRequest.setProcessDefinitionType(processType);

        assertEquals(processType, deployFlowRequest.getProcessDefinitionType());
    }

    @Test
    public void testSetAndGetProcessDefinitionCode() {
        // 测试设置和获取流程定义编码
        String processCode = "PROC_001";
        deployFlowRequest.setProcessDefinitionCode(processCode);

        assertEquals(processCode, deployFlowRequest.getProcessDefinitionCode());
    }

    @Test
    public void testSetAndGetProcessDefinitionDesc() {
        // 测试设置和获取流程定义描述
        String description = "Test process description";
        deployFlowRequest.setProcessDefinitionDesc(description);

        assertEquals(description, deployFlowRequest.getProcessDefinitionDesc());
    }

    @Test
    public void testCompleteDeployFlowRequestSetup() {
        // 测试完整的部署流程请求设置
        deployFlowRequest.setOperator("admin");
        deployFlowRequest.setDeploymentUserId("user123");
        deployFlowRequest.setProcessDefinitionName("TestProcess");
        deployFlowRequest.setProcessDefinitionType("BPMN");
        deployFlowRequest.setProcessDefinitionCode("TEST_001");
        deployFlowRequest.setProcessDefinitionId("proc-id-123");
        deployFlowRequest.setProcessDefinitionVersion("1.0");
        deployFlowRequest.setProcessDefinitionDesc("Test description");
        deployFlowRequest.setProcessDefinitionContent("<?xml version='1.0'?>");

        // 验证所有字段都正确设置
        assertEquals("admin", deployFlowRequest.getOperator());
        assertEquals("user123", deployFlowRequest.getDeploymentUserId());
        assertEquals("TestProcess", deployFlowRequest.getProcessDefinitionName());
        assertEquals("BPMN", deployFlowRequest.getProcessDefinitionType());
        assertEquals("TEST_001", deployFlowRequest.getProcessDefinitionCode());
        assertEquals("proc-id-123", deployFlowRequest.getProcessDefinitionId());
        assertEquals("1.0", deployFlowRequest.getProcessDefinitionVersion());
        assertEquals("Test description", deployFlowRequest.getProcessDefinitionDesc());
        assertEquals("<?xml version='1.0'?>", deployFlowRequest.getProcessDefinitionContent());
    }
}
