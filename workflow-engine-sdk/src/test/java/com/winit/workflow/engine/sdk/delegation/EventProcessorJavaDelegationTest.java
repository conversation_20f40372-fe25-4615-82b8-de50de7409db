package com.winit.workflow.engine.sdk.delegation;

import com.alibaba.smart.framework.engine.context.ExecutionContext;
import com.alibaba.smart.framework.engine.model.instance.ExecutionInstance;
import com.winit.workflow.engine.core.provider.smartengine.AbstractExpressionJavaDelegation;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * EventProcessorJavaDelegation的单元测试
 */
public class EventProcessorJavaDelegationTest {

    @Mock
    private ExecutionContext mockExecutionContext;

    @Mock
    private ExecutionInstance mockExecutionInstance;

    @Mock
    private PythonCallback mockCallback;

    private Map<String, Object> requestMap;
    private Map<String, Object> responseMap;
    private EventProcessorJavaDelegation delegation;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        // 准备请求和响应Map
        requestMap = new HashMap<>();
        responseMap = new HashMap<>();

        // 配置Mock对象行为
        Mockito.when(mockExecutionContext.getRequest()).thenReturn(requestMap);
        Mockito.when(mockExecutionContext.getResponse()).thenReturn(responseMap);
        Mockito.when(mockExecutionContext.getExecutionInstance()).thenReturn(mockExecutionInstance);
        Mockito.when(mockExecutionInstance.getProcessDefinitionActivityId()).thenReturn("test-activity-id");

        // 创建委托实例
        delegation = new EventProcessorJavaDelegation();
    }

    @Test
    public void testSuccessfulExecution() {
        // 准备测试数据
        requestMap.put("key1", "value1");
        requestMap.put("key2", 123);

        // 执行
        delegation.execute(mockExecutionContext);

        // 验证响应包含请求数据（由基类处理）
        assertTrue("Response should contain request data", responseMap.containsKey("key1"));
        assertTrue("Response should contain request data", responseMap.containsKey("key2"));
    }

    @Test
    public void testExecutionWithExpressions() {
        // 准备包含表达式的测试数据
        requestMap.put("greeting", "${concat('Hello, ', 'World')}");
        requestMap.put("number", 42);

        // 执行
        delegation.execute(mockExecutionContext);

        // 验证表达式被解析（由基类处理）
        assertEquals("Hello, World", requestMap.get("greeting"));
        assertEquals(42, requestMap.get("number"));
    }

    @Test
    public void testCallbackExecution() {
        // 设置自定义回调
        delegation.setCallback(mockCallback);

        // 准备测试数据
        requestMap.put("test", "data");

        // 执行
        delegation.execute(mockExecutionContext);

        // 验证回调被调用
        Mockito.verify(mockCallback, Mockito.times(1)).onEvent(ArgumentMatchers.eq(requestMap), ArgumentMatchers.eq(responseMap));
    }

    @Test
    public void testDefaultCallback() {
        // 验证默认回调不为null
        assertNotNull("Default callback should not be null", delegation.getCallback());
        assertTrue("Default callback should be PythonCallbackImpl",
                delegation.getCallback() instanceof PythonCallbackImpl);
    }

    @Test
    public void testSetAndGetCallback() {
        // 测试设置和获取回调
        delegation.setCallback(mockCallback);
        assertEquals("Callback should be set correctly", mockCallback, delegation.getCallback());
    }

    @Test
    public void testCallbackWithComplexData() {
        // 设置自定义回调
        delegation.setCallback(mockCallback);

        // 准备复杂的测试数据
        Map<String, Object> nestedMap = new HashMap<>();
        nestedMap.put("nested", "value");
        requestMap.put("complex", nestedMap);
        requestMap.put("expression", "${concat('test', 'value')}");

        // 执行
        delegation.execute(mockExecutionContext);

        // 验证回调被调用
        Mockito.verify(mockCallback, Mockito.times(1)).onEvent(ArgumentMatchers.any(Map.class), ArgumentMatchers.any(Map.class));

        // 验证表达式被处理
        assertEquals("testvalue", requestMap.get("expression"));
    }

    @Test(expected = RuntimeException.class)
    public void testExecutionWithNullContext() {
        // 执行 - 应该抛出异常
        delegation.execute(null);
    }

    @Test(expected = RuntimeException.class)
    public void testExecutionWithNullRequest() {
        // 配置Mock对象 - request为null
        Mockito.when(mockExecutionContext.getRequest()).thenReturn(null);

        // 执行 - 应该抛出异常
        delegation.execute(mockExecutionContext);
    }

    @Test(expected = RuntimeException.class)
    public void testExecutionWithNullResponse() {
        // 配置Mock对象 - response为null
        Mockito.when(mockExecutionContext.getResponse()).thenReturn(null);

        // 执行 - 应该抛出异常
        delegation.execute(mockExecutionContext);
    }

    @Test
    public void testInheritanceFromAbstractBase() {
        // 验证继承关系
        assertTrue("Should inherit from AbstractExpressionJavaDelegation",
                delegation instanceof AbstractExpressionJavaDelegation);
    }

    @Test
    public void testCallbackExecutionOrder() {
        // 创建一个记录调用顺序的回调
        TestOrderCallback orderCallback = new TestOrderCallback();
        delegation.setCallback(orderCallback);

        // 准备测试数据
        requestMap.put("test", "data");

        // 执行
        delegation.execute(mockExecutionContext);

        // 验证回调被调用
        assertTrue("Callback should be executed", orderCallback.wasExecuted());
    }

    @Test
    public void testExpressionProcessingInheritance() {
        // 准备包含嵌套表达式的数据
        Map<String, Object> nestedData = new HashMap<>();
        nestedData.put("inner", "${concat('nested', 'expression')}");
        requestMap.put("outer", nestedData);

        // 执行
        delegation.execute(mockExecutionContext);

        // 验证嵌套表达式被处理（由基类处理）
        @SuppressWarnings("unchecked")
        Map<String, Object> processedNested = (Map<String, Object>) requestMap.get("outer");
        assertEquals("nestedexpression", processedNested.get("inner"));
    }

    /**
     * 测试用的回调实现，用于验证调用顺序
     */
    private static class TestOrderCallback implements PythonCallback {
        private boolean executed = false;

        @Override
        public void onEvent(Map<String, Object> context, Map<String, Object> response) {
            executed = true;
        }

        public boolean wasExecuted() {
            return executed;
        }
    }
}
