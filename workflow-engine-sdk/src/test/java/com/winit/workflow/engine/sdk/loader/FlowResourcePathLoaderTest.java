package com.winit.workflow.engine.sdk.loader;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

public class FlowResourcePathLoaderTest {

    @Before
    public void setUp() {
    }

    @After
    public void tearDown() {
    }

    @Test
    public void testLoad() {
        FlowResourcePathLoader flowResourcePathLoader = new FlowResourcePathLoader("workflow-engine/test.bpmn.xml");
        String load = flowResourcePathLoader.load();
        Assert.assertNotNull(load);

        FlowResourcePathLoader flowResourcePathLoader_1 = new FlowResourcePathLoader("workflow-engine/test_1.bpmn.xml");
        String load_1 = flowResourcePathLoader_1.load();
        Assert.assertNotNull(load_1);
    }
}