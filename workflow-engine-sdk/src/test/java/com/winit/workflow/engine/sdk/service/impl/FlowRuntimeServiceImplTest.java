package com.winit.workflow.engine.sdk.service.impl;

import com.winit.workflow.engine.sdk.base.WorkflowBaseTest;
import com.winit.workflow.engine.sdk.loader.FlowContentLoader;
import com.winit.workflow.engine.sdk.loader.FlowResourcePathLoader;
import com.winit.workflow.engine.sdk.service.FlowRuntimeService;
import com.winit.workflow.engine.sdk.vo.request.FlowStartRequest;
import com.winit.workflow.engine.sdk.vo.response.FlowStartResponse;
import org.junit.*;

import java.util.HashMap;
import java.util.Map;


public class FlowRuntimeServiceImplTest extends WorkflowBaseTest {

    private FlowRuntimeService flowRuntimeService;


    @Before
    public void setUp() {
        super.setUp();
        flowRuntimeService = new FlowRuntimeServiceImpl(workflowEngine);
    }

    @After
    public void tearDown() {
        super.tearDown();
    }

    @Test
    public void testExecute() {
        FlowResourcePathLoader flowResourcePathLoader = new FlowResourcePathLoader("workflow-engine/default-sequence-flow.bpmn.xml");
        FlowStartRequest<FlowContentLoader> flowStartRequest = new FlowStartRequest<FlowContentLoader>();
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("route", "NoRoute");
        flowStartRequest.setVariable(parameters);
        flowStartRequest.setFlowContent(flowResourcePathLoader);
        FlowStartResponse flowStartResponse = flowRuntimeService.execute(flowStartRequest);
        Map<String, Object> variables = flowStartResponse.getResponse();
        Object route = variables.get("route");
        Object processDefinitionActivityId = variables.get("defaultSequenceFlow");
        Assert.assertEquals("NoRoute", route);
        Assert.assertEquals("serviceTask1", processDefinitionActivityId);


    }

    @Test
    public void testExecutev2() {
        FlowResourcePathLoader flowResourcePathLoader = new FlowResourcePathLoader("workflow-engine/bpmn_if.bpmn.xml");
        FlowStartRequest<FlowContentLoader> flowStartRequest = new FlowStartRequest<FlowContentLoader>();
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("index", 0);
        parameters.put("count", 0);
        parameters.put("login_info", false);
        flowStartRequest.setVariable(parameters);
        flowStartRequest.setFlowContent(flowResourcePathLoader);
        FlowStartResponse flowStartResponse = flowRuntimeService.execute(flowStartRequest);
        Map<String, Object> variables = flowStartResponse.getResponse();
        Object index = variables.get("index");
        Object count = variables.get("count");
        Object login_info = variables.get("login_info");
        System.out.println(index);
        System.out.println(count);
        System.out.println(login_info);

    }

    @Test
    public void testExecutev3() {
        FlowResourcePathLoader flowResourcePathLoader = new FlowResourcePathLoader("workflow-engine/bpmn_while.bpmn.xml");
        FlowStartRequest<FlowContentLoader> flowStartRequest = new FlowStartRequest<FlowContentLoader>();
        // 创建用户对象
        Map<String, Object> user = new HashMap<>();
        user.put("name", "张三");
        user.put("age", 30);
        user.put("department", "技术部");
        // 创建流程变量

        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("user", user);
        parameters.put("cycle_index", 0);
        parameters.put("cycle_count", 2);
        flowStartRequest.setVariable(parameters);
        flowStartRequest.setFlowContent(flowResourcePathLoader);
        FlowStartResponse flowStartResponse = flowRuntimeService.execute(flowStartRequest);
        Map<String, Object> variables = flowStartResponse.getResponse();
        Object index = variables.get("cycle_index");
        Object count = variables.get("cycle_count");
        System.out.println(index);
        System.out.println(count);
        System.out.println(variables);
    }

    @Test
    public void testExecute4FlowRuntimeService() {

        FlowResourcePathLoader flowResourcePathLoader = new FlowResourcePathLoader("workflow-engine/default-sequence-flow.bpmn.xml");
        FlowStartRequest<FlowContentLoader> flowStartRequest = new FlowStartRequest<FlowContentLoader>();
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("route", "NoRoute");
        flowStartRequest.setVariable(parameters);
        flowStartRequest.setFlowContent(flowResourcePathLoader);
        FlowStartResponse flowStartResponse = flowRuntimeService.execute(flowStartRequest);
        Map<String, Object> variables = flowStartResponse.getResponse();
        Object route = variables.get("route");
        Object processDefinitionActivityId = variables.get("defaultSequenceFlow");
        Assert.assertEquals("NoRoute", route);
        Assert.assertEquals("serviceTask1", processDefinitionActivityId);


    }

    @Test
    @Ignore("需要外部API服务运行，跳过集成测试")
    public void testExecute4RemoteLoader() {

        FlowContentLoader flowResourcePathLoader = flowResourceApiLoader.of("ghjk-fgdhjk-fgvbnm-lkjnbv", "1.0.0");
        FlowStartRequest<FlowContentLoader> flowStartRequest = new FlowStartRequest<FlowContentLoader>();
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("route", "NoRoute");
        flowStartRequest.setVariable(parameters);
        flowStartRequest.setFlowContent(flowResourcePathLoader);
        FlowStartResponse flowStartResponse = flowRuntimeService.execute(flowStartRequest);
        Map<String, Object> variables = flowStartResponse.getResponse();
        Object route = variables.get("route");
        Object processDefinitionActivityId = variables.get("defaultSequenceFlow");
        Assert.assertEquals("NoRoute", route);
        Assert.assertEquals("serviceTask1", processDefinitionActivityId);

    }
}