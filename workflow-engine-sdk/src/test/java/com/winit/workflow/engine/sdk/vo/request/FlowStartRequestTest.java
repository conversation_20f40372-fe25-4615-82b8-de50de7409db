package com.winit.workflow.engine.sdk.vo.request;

import com.winit.workflow.engine.sdk.loader.FlowContentLoader;
import com.winit.workflow.engine.sdk.loader.FlowResourcePathLoader;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * FlowStartRequest的单元测试
 * 使用JUnit 4框架进行测试
 */
public class FlowStartRequestTest {

    @Mock
    private FlowContentLoader mockFlowContentLoader;

    private FlowStartRequest<FlowContentLoader> flowStartRequest;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        flowStartRequest = new FlowStartRequest<>();
    }

    @Test
    public void testDefaultConstructor() {
        // 测试默认构造函数
        FlowStartRequest<FlowContentLoader> request = new FlowStartRequest<>();
        
        assertNotNull(request);
        assertNull(request.getFlowContent());
        assertNull(request.getVariable());
        assertNull(request.getOperator()); // 继承自BaseRequest
    }

    @Test
    public void testSetAndGetFlowContent() {
        // 测试设置和获取FlowContent
        flowStartRequest.setFlowContent(mockFlowContentLoader);
        
        assertSame(mockFlowContentLoader, flowStartRequest.getFlowContent());
    }

    @Test
    public void testSetAndGetFlowContentWithNull() {
        // 测试设置null FlowContent
        flowStartRequest.setFlowContent(null);
        
        assertNull(flowStartRequest.getFlowContent());
    }

    @Test
    public void testSetAndGetVariable() {
        // 测试设置和获取变量Map
        Map<String, Object> variables = new HashMap<>();
        variables.put("key1", "value1");
        variables.put("key2", 123);
        variables.put("key3", true);
        
        flowStartRequest.setVariable(variables);
        
        assertSame(variables, flowStartRequest.getVariable());
        assertEquals("value1", flowStartRequest.getVariable().get("key1"));
        assertEquals(123, flowStartRequest.getVariable().get("key2"));
        assertEquals(true, flowStartRequest.getVariable().get("key3"));
    }

    @Test
    public void testSetAndGetVariableWithNull() {
        // 测试设置null变量
        flowStartRequest.setVariable(null);
        
        assertNull(flowStartRequest.getVariable());
    }

    @Test
    public void testSetAndGetVariableWithEmptyMap() {
        // 测试设置空的变量Map
        Map<String, Object> emptyVariables = new HashMap<>();
        flowStartRequest.setVariable(emptyVariables);
        
        assertSame(emptyVariables, flowStartRequest.getVariable());
        assertTrue(flowStartRequest.getVariable().isEmpty());
    }

    @Test
    public void testSetAndGetOperator() {
        // 测试设置和获取操作人（继承自BaseRequest）
        String operator = "testUser";
        flowStartRequest.setOperator(operator);
        
        assertEquals(operator, flowStartRequest.getOperator());
    }

    @Test
    public void testCompleteRequestSetup() {
        // 测试完整的请求设置
        String operator = "admin";
        Map<String, Object> variables = new HashMap<>();
        variables.put("processId", "test-process");
        variables.put("priority", "high");
        
        flowStartRequest.setOperator(operator);
        flowStartRequest.setFlowContent(mockFlowContentLoader);
        flowStartRequest.setVariable(variables);
        
        assertEquals(operator, flowStartRequest.getOperator());
        assertSame(mockFlowContentLoader, flowStartRequest.getFlowContent());
        assertSame(variables, flowStartRequest.getVariable());
        assertEquals("test-process", flowStartRequest.getVariable().get("processId"));
        assertEquals("high", flowStartRequest.getVariable().get("priority"));
    }

    @Test
    public void testWithRealFlowContentLoader() {
        // 测试使用真实的FlowContentLoader
        FlowResourcePathLoader realLoader = new FlowResourcePathLoader("test-path");
        flowStartRequest.setFlowContent(realLoader);
        
        assertSame(realLoader, flowStartRequest.getFlowContent());
        assertTrue(flowStartRequest.getFlowContent() instanceof FlowResourcePathLoader);
    }

    @Test
    public void testVariableMapModification() {
        // 测试变量Map的修改
        Map<String, Object> variables = new HashMap<>();
        variables.put("initial", "value");
        
        flowStartRequest.setVariable(variables);
        
        // 通过getter获取Map并修改
        flowStartRequest.getVariable().put("added", "newValue");
        flowStartRequest.getVariable().remove("initial");
        
        assertNull(flowStartRequest.getVariable().get("initial"));
        assertEquals("newValue", flowStartRequest.getVariable().get("added"));
    }

    @Test
    public void testVariableMapWithComplexObjects() {
        // 测试变量Map包含复杂对象
        Map<String, Object> variables = new HashMap<>();
        
        // 添加各种类型的对象
        variables.put("string", "text");
        variables.put("integer", 42);
        variables.put("double", 3.14);
        variables.put("boolean", false);
        variables.put("array", new int[]{1, 2, 3});
        
        Map<String, String> nestedMap = new HashMap<>();
        nestedMap.put("nested", "value");
        variables.put("map", nestedMap);
        
        flowStartRequest.setVariable(variables);
        
        assertEquals("text", flowStartRequest.getVariable().get("string"));
        assertEquals(42, flowStartRequest.getVariable().get("integer"));
        assertEquals(3.14, flowStartRequest.getVariable().get("double"));
        assertEquals(false, flowStartRequest.getVariable().get("boolean"));
        assertArrayEquals(new int[]{1, 2, 3}, (int[]) flowStartRequest.getVariable().get("array"));
        assertEquals(nestedMap, flowStartRequest.getVariable().get("map"));
    }

    @Test
    public void testToStringMethod() {
        // 测试toString方法（如果Lombok生成了的话）
        flowStartRequest.setOperator("testUser");
        flowStartRequest.setFlowContent(mockFlowContentLoader);
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("test", "value");
        flowStartRequest.setVariable(variables);
        
        String toString = flowStartRequest.toString();
        assertNotNull(toString);
        // toString应该包含类名
        assertTrue(toString.contains("FlowStartRequest"));
    }

    @Test
    public void testEqualsAndHashCode() {
        // 测试equals和hashCode方法（如果Lombok生成了的话）
        FlowStartRequest<FlowContentLoader> request1 = new FlowStartRequest<>();
        FlowStartRequest<FlowContentLoader> request2 = new FlowStartRequest<>();
        
        // 设置相同的值
        request1.setOperator("user");
        request2.setOperator("user");
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("key", "value");
        request1.setVariable(variables);
        request2.setVariable(variables);
        
        request1.setFlowContent(mockFlowContentLoader);
        request2.setFlowContent(mockFlowContentLoader);
        
        // 测试equals
        assertEquals(request1, request2);
        assertEquals(request1.hashCode(), request2.hashCode());
    }

    @Test
    public void testInheritanceFromBaseRequest() {
        // 测试继承关系
        assertTrue(flowStartRequest instanceof BaseRequest);
        
        // 测试可以调用父类方法
        flowStartRequest.setOperator("inheritanceTest");
        assertEquals("inheritanceTest", flowStartRequest.getOperator());
    }

    @Test
    public void testGenericTypeParameter() {
        // 测试泛型类型参数
        FlowStartRequest<FlowResourcePathLoader> specificRequest = new FlowStartRequest<>();
        FlowResourcePathLoader specificLoader = new FlowResourcePathLoader("test");
        
        specificRequest.setFlowContent(specificLoader);
        
        assertSame(specificLoader, specificRequest.getFlowContent());
        assertTrue(specificRequest.getFlowContent() instanceof FlowResourcePathLoader);
    }
}
