package com.winit.workflow.engine.sdk.service.impl;

import com.winit.workflow.engine.sdk.base.WorkflowBaseTest;
import com.winit.workflow.engine.sdk.loader.FlowContentLoader;
import com.winit.workflow.engine.sdk.loader.FlowResourcePathLoader;
import com.winit.workflow.engine.sdk.service.FlowRuntimeService;
import com.winit.workflow.engine.sdk.vo.request.FlowStartRequest;
import com.winit.workflow.engine.sdk.vo.response.FlowStartResponse;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

public class FlowRuntimeServiceImpl2Test extends WorkflowBaseTest {


    @Before
    public void setUp() {

    }

    @After
    public void tearDown() {
        super.tearDown();
    }


    @Test
    public void testExecute4FlowRuntimeService() {
        FlowRuntimeService flowRuntimeService = new FlowRuntimeServiceImpl(workflowEngine);
        FlowResourcePathLoader flowResourcePathLoader = new FlowResourcePathLoader("/Users/<USER>/CodeSpace/workflow-engine/workflow-engine-sdk/src/test/resources/workflow-engine/default-sequence-flow.bpmn.xml");
        FlowStartRequest<FlowContentLoader> flowStartRequest = new FlowStartRequest<FlowContentLoader>();
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("route", "NoRoute");
        flowStartRequest.setVariable(parameters);
        flowStartRequest.setFlowContent(flowResourcePathLoader);
        FlowStartResponse flowStartResponse = flowRuntimeService.execute(flowStartRequest);
        Map<String, Object> variables = flowStartResponse.getResponse();
        Object route = variables.get("route");
        Object processDefinitionActivityId = variables.get("defaultSequenceFlow");
        Assert.assertEquals("NoRoute", route);
        Assert.assertEquals("serviceTask1", processDefinitionActivityId);


    }


    @Test
    public void testExecute4CamundaFlowRuntimeService() {
        FlowRuntimeService flowRuntimeService =new FlowRuntimeServiceImpl(workflowEngine);
        FlowResourcePathLoader flowResourcePathLoader = new FlowResourcePathLoader("/Users/<USER>/CodeSpace/workflow-engine/workflow-engine-sdk/src/test/resources/workflow-engine/camunda-example-process.bpmn");
        FlowStartRequest<FlowContentLoader> flowStartRequest = new FlowStartRequest<FlowContentLoader>();
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("route", "NoRoute");
        parameters.put("flag",0);
        flowStartRequest.setVariable(parameters);
        flowStartRequest.setFlowContent(flowResourcePathLoader);
        FlowStartResponse flowStartResponse = flowRuntimeService.execute(flowStartRequest);
        Map<String, Object> variables = flowStartResponse.getResponse();
        Object route = variables.get("route");
        Object processDefinitionActivityId = variables.get("defaultSequenceFlow");
        Assert.assertEquals("NoRoute", route);
        Assert.assertEquals("littleTask", processDefinitionActivityId);


    }

}