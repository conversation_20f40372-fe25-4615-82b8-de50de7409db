package com.winit.workflow.engine.sdk.vo.request;

import com.winit.workflow.engine.sdk.loader.FlowContentLoader;
import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * BaseRequest的单元测试
 * 使用JUnit 4框架进行测试
 */
public class BaseRequestTest {

    private BaseRequest baseRequest;

    @Before
    public void setUp() {
        // 创建BaseRequest的具体实现用于测试
        baseRequest = new BaseRequest() {
            // 匿名内部类实现，用于测试抽象基类
        };
    }

    @Test
    public void testDefaultConstructor() {
        // 测试默认构造函数
        BaseRequest request = new BaseRequest() {};
        
        assertNotNull(request);
        assertNull(request.getOperator());
    }

    @Test
    public void testSetAndGetOperator() {
        // 测试设置和获取操作人
        String operator = "testUser";
        baseRequest.setOperator(operator);
        
        assertEquals(operator, baseRequest.getOperator());
    }

    @Test
    public void testSetAndGetOperatorWithNull() {
        // 测试设置null操作人
        baseRequest.setOperator(null);
        
        assertNull(baseRequest.getOperator());
    }

    @Test
    public void testSetAndGetOperatorWithEmptyString() {
        // 测试设置空字符串操作人
        baseRequest.setOperator("");
        
        assertEquals("", baseRequest.getOperator());
    }

    @Test
    public void testSetAndGetOperatorWithWhitespace() {
        // 测试设置包含空白字符的操作人
        String operatorWithSpaces = "  user with spaces  ";
        baseRequest.setOperator(operatorWithSpaces);
        
        assertEquals(operatorWithSpaces, baseRequest.getOperator());
    }

    @Test
    public void testMultipleOperatorChanges() {
        // 测试多次更改操作人
        baseRequest.setOperator("user1");
        assertEquals("user1", baseRequest.getOperator());
        
        baseRequest.setOperator("user2");
        assertEquals("user2", baseRequest.getOperator());
        
        baseRequest.setOperator(null);
        assertNull(baseRequest.getOperator());
        
        baseRequest.setOperator("user3");
        assertEquals("user3", baseRequest.getOperator());
    }

    @Test
    public void testOperatorWithSpecialCharacters() {
        // 测试操作人包含特殊字符
        String specialOperator = "用户@domain.com & <admin> \"test\"";
        baseRequest.setOperator(specialOperator);
        
        assertEquals(specialOperator, baseRequest.getOperator());
    }

    @Test
    public void testOperatorWithLongString() {
        // 测试长操作人名称
        StringBuilder longOperator = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longOperator.append("a");
        }
        
        baseRequest.setOperator(longOperator.toString());
        
        assertEquals(longOperator.toString(), baseRequest.getOperator());
        assertEquals(1000, baseRequest.getOperator().length());
    }

    @Test
    public void testOperatorConsistency() {
        // 测试操作人设置的一致性
        String operator = "consistencyTest";
        baseRequest.setOperator(operator);
        
        // 多次获取应该返回相同的值
        assertEquals(operator, baseRequest.getOperator());
        assertEquals(operator, baseRequest.getOperator());
        assertEquals(operator, baseRequest.getOperator());
    }

    @Test
    public void testToStringMethod() {
        // 测试toString方法（如果Lombok生成了的话）
        baseRequest.setOperator("testUser");
        
        String toString = baseRequest.toString();
        assertNotNull(toString);
        // toString应该包含类信息
        assertTrue(toString.length() > 0);
    }

    @Test
    public void testEqualsAndHashCode() {
        // 测试equals和hashCode方法（如果Lombok生成了的话）
        BaseRequest request1 = new BaseRequest() {};
        BaseRequest request2 = new BaseRequest() {};
        
        // 设置相同的值
        request1.setOperator("sameUser");
        request2.setOperator("sameUser");
        
        // 测试equals
        assertEquals(request1, request2);
        assertEquals(request1.hashCode(), request2.hashCode());
    }

    @Test
    public void testEqualsWithDifferentOperators() {
        // 测试不同操作人的equals
        BaseRequest request1 = new BaseRequest() {};
        BaseRequest request2 = new BaseRequest() {};
        
        request1.setOperator("user1");
        request2.setOperator("user2");
        
        // 应该不相等
        assertNotEquals(request1, request2);
    }

    @Test
    public void testEqualsWithNullOperators() {
        // 测试null操作人的equals
        BaseRequest request1 = new BaseRequest() {};
        BaseRequest request2 = new BaseRequest() {};
        
        request1.setOperator(null);
        request2.setOperator(null);
        
        // 应该相等
        assertEquals(request1, request2);
        assertEquals(request1.hashCode(), request2.hashCode());
    }

    @Test
    public void testEqualsWithOneNullOperator() {
        // 测试一个null操作人的equals
        BaseRequest request1 = new BaseRequest() {};
        BaseRequest request2 = new BaseRequest() {};
        
        request1.setOperator("user");
        request2.setOperator(null);
        
        // 应该不相等
        assertNotEquals(request1, request2);
    }

    @Test
    public void testInheritance() {
        // 测试继承关系
        assertTrue(baseRequest instanceof BaseRequest);
        assertTrue(baseRequest instanceof Object);
    }

    @Test
    public void testConcreteImplementations() {
        // 测试具体实现类
        FlowStartRequest<FlowContentLoader> flowStartRequest = new FlowStartRequest<>();
        DeployFlowRequest deployFlowRequest = new DeployFlowRequest();

        assertTrue(flowStartRequest instanceof BaseRequest);
        assertTrue(deployFlowRequest instanceof BaseRequest);

        // 测试多态性
        BaseRequest baseRef1 = flowStartRequest;
        BaseRequest baseRef2 = deployFlowRequest;

        baseRef1.setOperator("polymorphismTest1");
        baseRef2.setOperator("polymorphismTest2");

        assertEquals("polymorphismTest1", baseRef1.getOperator());
        assertEquals("polymorphismTest2", baseRef2.getOperator());
        assertEquals("polymorphismTest1", flowStartRequest.getOperator());
        assertEquals("polymorphismTest2", deployFlowRequest.getOperator());
    }

    @Test
    public void testOperatorImmutability() {
        // 测试操作人字符串的不可变性
        String originalOperator = "immutableTest";
        baseRequest.setOperator(originalOperator);
        
        String retrievedOperator = baseRequest.getOperator();
        assertEquals(originalOperator, retrievedOperator);
        
        // 修改原始字符串引用不应该影响存储的值
        originalOperator = "modified";
        assertEquals("immutableTest", baseRequest.getOperator());
    }

    @Test
    public void testOperatorThreadSafety() {
        // 基本的线程安全测试
        final String[] operators = {"user1", "user2", "user3", "user4", "user5"};
        final BaseRequest threadSafeRequest = new BaseRequest() {};
        
        // 简单的并发设置测试
        for (String operator : operators) {
            threadSafeRequest.setOperator(operator);
            assertEquals(operator, threadSafeRequest.getOperator());
        }
    }
}
