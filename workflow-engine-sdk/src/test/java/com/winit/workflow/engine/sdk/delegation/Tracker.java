package com.winit.workflow.engine.sdk.delegation;

import com.alibaba.smart.framework.engine.constant.ExtensionElementsConstant;
import com.alibaba.smart.framework.engine.context.ExecutionContext;
import com.alibaba.smart.framework.engine.delegation.JavaDelegation;
import com.alibaba.smart.framework.engine.model.assembly.ExtensionElementContainer;
import com.alibaba.smart.framework.engine.model.assembly.ExtensionElements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * <AUTHOR>
 * Created by ettear on 06/08/2017.
 */
public class Tracker implements JavaDelegation {
    private static final Logger LOGGER = LoggerFactory.getLogger(SimpleServiceTaskDelegation.class);
    public static final String DEFAULT_SEQUENCE_FLOW = "defaultSequenceFlow";


    @Override
    public void execute(ExecutionContext executionContext) {

        String processDefinitionActivityId = executionContext.getExecutionInstance().getProcessDefinitionActivityId();

        ExtensionElementContainer idBasedElement = (ExtensionElementContainer) executionContext.getProcessDefinition().getIdBasedElementMap().get(
                processDefinitionActivityId);

        ExtensionElements extensionElements = idBasedElement.getExtensionElements();
        if (null != extensionElements) {

            Map map = (Map) extensionElements.getDecorationMap().get(
                    ExtensionElementsConstant.PROPERTIES);

            executionContext.getResponse().putAll(map);
            executionContext.getResponse().putAll(executionContext.getRequest());

            LOGGER.info("Executing service task for activity: {}", executionContext.getExecutionInstance().getProcessDefinitionActivityId());
            executionContext.getResponse().put(DEFAULT_SEQUENCE_FLOW, executionContext.getExecutionInstance().getProcessDefinitionActivityId());

        }

    }
}

