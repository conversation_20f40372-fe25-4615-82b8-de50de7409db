package com.winit.workflow.engine.sdk.vo.response;

import org.junit.Before;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * FlowStartResponse的单元测试
 * 使用JUnit 4框架进行测试
 */
public class FlowStartResponseTest {

    private FlowStartResponse flowStartResponse;

    @Before
    public void setUp() {
        flowStartResponse = new FlowStartResponse();
    }

    @Test
    public void testDefaultConstructor() {
        // 测试默认构造函数
        FlowStartResponse response = new FlowStartResponse();
        
        assertNotNull(response);
        assertNotNull(response.getResponse());
        assertTrue(response.getResponse().isEmpty());
        assertEquals(0, response.getErrCode()); // 继承自BaseResponse
        assertNull(response.getErrMsg()); // 继承自BaseResponse
    }

    @Test
    public void testGetResponseNotNull() {
        // 测试response字段默认不为null
        assertNotNull(flowStartResponse.getResponse());
        assertTrue(flowStartResponse.getResponse() instanceof HashMap);
    }

    @Test
    public void testSetAndGetResponse() {
        // 测试设置和获取response
        Map<String, Object> responseMap = new HashMap<>();
        responseMap.put("result", "success");
        responseMap.put("processInstanceId", "12345");
        responseMap.put("timestamp", System.currentTimeMillis());
        
        flowStartResponse.setResponse(responseMap);
        
        assertSame(responseMap, flowStartResponse.getResponse());
        assertEquals("success", flowStartResponse.getResponse().get("result"));
        assertEquals("12345", flowStartResponse.getResponse().get("processInstanceId"));
    }

    @Test
    public void testSetResponseWithNull() {
        // 测试设置null response
        flowStartResponse.setResponse(null);
        
        assertNull(flowStartResponse.getResponse());
    }

    @Test
    public void testResponseMapModification() {
        // 测试response Map的修改
        Map<String, Object> originalResponse = flowStartResponse.getResponse();
        
        // 添加数据
        originalResponse.put("key1", "value1");
        originalResponse.put("key2", 123);
        
        assertEquals("value1", flowStartResponse.getResponse().get("key1"));
        assertEquals(123, flowStartResponse.getResponse().get("key2"));
        assertEquals(2, flowStartResponse.getResponse().size());
    }

    @Test
    public void testResponseWithComplexObjects() {
        // 测试response包含复杂对象
        Map<String, Object> response = flowStartResponse.getResponse();
        
        // 添加各种类型的对象
        response.put("string", "text");
        response.put("integer", 42);
        response.put("double", 3.14);
        response.put("boolean", true);
        response.put("array", new String[]{"a", "b", "c"});
        
        Map<String, Object> nestedMap = new HashMap<>();
        nestedMap.put("nested", "value");
        response.put("object", nestedMap);
        
        assertEquals("text", response.get("string"));
        assertEquals(42, response.get("integer"));
        assertEquals(3.14, response.get("double"));
        assertEquals(true, response.get("boolean"));
        assertArrayEquals(new String[]{"a", "b", "c"}, (String[]) response.get("array"));
        assertEquals(nestedMap, response.get("object"));
    }

    @Test
    public void testInheritanceFromBaseResponse() {
        // 测试继承关系
        assertTrue(flowStartResponse instanceof BaseResponse);
        
        // 测试可以调用父类方法
        flowStartResponse.setErrCode(404);
        flowStartResponse.setErrMsg("Not Found");
        
        assertEquals(404, flowStartResponse.getErrCode());
        assertEquals("Not Found", flowStartResponse.getErrMsg());
    }

    @Test
    public void testSetAndGetErrCode() {
        // 测试设置和获取错误码
        flowStartResponse.setErrCode(500);
        assertEquals(500, flowStartResponse.getErrCode());
        
        flowStartResponse.setErrCode(0);
        assertEquals(0, flowStartResponse.getErrCode());
        
        flowStartResponse.setErrCode(-1);
        assertEquals(-1, flowStartResponse.getErrCode());
    }

    @Test
    public void testSetAndGetErrMsg() {
        // 测试设置和获取错误消息
        String errorMsg = "Internal Server Error";
        flowStartResponse.setErrMsg(errorMsg);
        
        assertEquals(errorMsg, flowStartResponse.getErrMsg());
    }

    @Test
    public void testSetAndGetErrMsgWithNull() {
        // 测试设置null错误消息
        flowStartResponse.setErrMsg(null);
        
        assertNull(flowStartResponse.getErrMsg());
    }

    @Test
    public void testSetAndGetErrMsgWithEmptyString() {
        // 测试设置空字符串错误消息
        flowStartResponse.setErrMsg("");
        
        assertEquals("", flowStartResponse.getErrMsg());
    }

    @Test
    public void testCompleteResponseSetup() {
        // 测试完整的响应设置
        Map<String, Object> responseData = new HashMap<>();
        responseData.put("processId", "proc-123");
        responseData.put("status", "completed");
        responseData.put("duration", 1500L);
        
        flowStartResponse.setResponse(responseData);
        flowStartResponse.setErrCode(200);
        flowStartResponse.setErrMsg("Success");
        
        assertEquals(responseData, flowStartResponse.getResponse());
        assertEquals(200, flowStartResponse.getErrCode());
        assertEquals("Success", flowStartResponse.getErrMsg());
        assertEquals("proc-123", flowStartResponse.getResponse().get("processId"));
        assertEquals("completed", flowStartResponse.getResponse().get("status"));
        assertEquals(1500L, flowStartResponse.getResponse().get("duration"));
    }

    @Test
    public void testErrorResponse() {
        // 测试错误响应
        flowStartResponse.setErrCode(400);
        flowStartResponse.setErrMsg("Bad Request");
        
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("error", "Invalid input");
        errorDetails.put("field", "flowContent");
        flowStartResponse.setResponse(errorDetails);
        
        assertEquals(400, flowStartResponse.getErrCode());
        assertEquals("Bad Request", flowStartResponse.getErrMsg());
        assertEquals("Invalid input", flowStartResponse.getResponse().get("error"));
        assertEquals("flowContent", flowStartResponse.getResponse().get("field"));
    }

    @Test
    public void testSuccessResponse() {
        // 测试成功响应
        flowStartResponse.setErrCode(0);
        flowStartResponse.setErrMsg(null);
        
        Map<String, Object> successData = new HashMap<>();
        successData.put("processInstanceId", "instance-456");
        successData.put("startTime", "2023-01-01T10:00:00Z");
        successData.put("variables", new HashMap<String, Object>());
        flowStartResponse.setResponse(successData);
        
        assertEquals(0, flowStartResponse.getErrCode());
        assertNull(flowStartResponse.getErrMsg());
        assertEquals("instance-456", flowStartResponse.getResponse().get("processInstanceId"));
        assertEquals("2023-01-01T10:00:00Z", flowStartResponse.getResponse().get("startTime"));
        assertNotNull(flowStartResponse.getResponse().get("variables"));
    }

    @Test
    public void testToStringMethod() {
        // 测试toString方法（如果Lombok生成了的话）
        flowStartResponse.setErrCode(200);
        flowStartResponse.setErrMsg("OK");
        flowStartResponse.getResponse().put("test", "value");
        
        String toString = flowStartResponse.toString();
        assertNotNull(toString);
        // toString应该包含类名
        assertTrue(toString.contains("FlowStartResponse"));
    }

    @Test
    public void testEqualsAndHashCode() {
        // 测试equals和hashCode方法（如果Lombok生成了的话）
        FlowStartResponse response1 = new FlowStartResponse();
        FlowStartResponse response2 = new FlowStartResponse();
        
        // 设置相同的值
        response1.setErrCode(200);
        response2.setErrCode(200);
        response1.setErrMsg("OK");
        response2.setErrMsg("OK");
        
        Map<String, Object> data = new HashMap<>();
        data.put("key", "value");
        response1.setResponse(data);
        response2.setResponse(data);
        
        // 测试equals
        assertEquals(response1, response2);
        assertEquals(response1.hashCode(), response2.hashCode());
    }

    @Test
    public void testResponseMapClear() {
        // 测试清空response Map
        Map<String, Object> response = flowStartResponse.getResponse();
        response.put("key1", "value1");
        response.put("key2", "value2");
        
        assertEquals(2, response.size());
        
        response.clear();
        
        assertEquals(0, response.size());
        assertTrue(response.isEmpty());
    }

    @Test
    public void testResponseMapPutAll() {
        // 测试批量添加到response Map
        Map<String, Object> sourceMap = new HashMap<>();
        sourceMap.put("batch1", "value1");
        sourceMap.put("batch2", "value2");
        sourceMap.put("batch3", "value3");
        
        flowStartResponse.getResponse().putAll(sourceMap);
        
        assertEquals(3, flowStartResponse.getResponse().size());
        assertEquals("value1", flowStartResponse.getResponse().get("batch1"));
        assertEquals("value2", flowStartResponse.getResponse().get("batch2"));
        assertEquals("value3", flowStartResponse.getResponse().get("batch3"));
    }

    @Test
    public void testResponseWithNullValues() {
        // 测试response包含null值
        Map<String, Object> response = flowStartResponse.getResponse();
        response.put("nullValue", null);
        response.put("notNull", "value");
        
        assertNull(response.get("nullValue"));
        assertEquals("value", response.get("notNull"));
        assertTrue(response.containsKey("nullValue"));
        assertEquals(2, response.size());
    }
}
