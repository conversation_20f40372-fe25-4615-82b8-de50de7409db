package com.winit.workflow.engine.sdk.expression.function;

import com.winit.workflow.engine.core.expression.function.ExpressionFunction;

/**
 * 生成订单号函数示例
 * 演示如何实现无参数的表达式函数
 */
public class GenerateOrderNumberFunction implements ExpressionFunction {
    
    @Override
    public String getFunctionName() {
        return "generateOrderNumber";
    }
    
    @Override
    public String getDescription() {
        return "Generates a unique order number";
    }
    
    @Override
    public int getParameterCount() {
        return 0; // 无参数函数
    }
    
    @Override
    public int getPriority() {
        return 100; // 自定义函数使用默认优先级
    }
    
    @Override
    public Object execute(Object... args) {
        return "ORD-" + System.currentTimeMillis();
    }
    
    @Override
    public boolean validateParameters(Object... args) {
        return args == null || args.length == 0;
    }
}
