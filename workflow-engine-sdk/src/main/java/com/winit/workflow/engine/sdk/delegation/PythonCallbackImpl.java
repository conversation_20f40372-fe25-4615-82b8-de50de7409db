package com.winit.workflow.engine.sdk.delegation;// PythonCallback.java

import java.util.Map;

/**
 * <AUTHOR>
public class PythonCallbackImpl implements PythonCallback {
    @Override
    public void onEvent(Map<String, Object> request, Map<String, Object> response) {
        System.out.println("PythonCallbackImpl onEvent");
        Object o = request.get("cycle_index");
        int cycle_index = o == null ? 0: Integer.parseInt(o.toString());
        request.put("cycle_index", cycle_index + 1);
        System.out.println(request);
        System.out.println(response);
    }
}