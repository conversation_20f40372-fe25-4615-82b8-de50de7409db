package com.winit.workflow.engine.sdk.service;

import com.winit.workflow.engine.sdk.loader.FlowContentLoader;
import com.winit.workflow.engine.sdk.vo.request.FlowStartRequest;
import com.winit.workflow.engine.sdk.vo.response.FlowStartResponse;


/**
 * 流程运行服务
 *
 * <AUTHOR>
 */
public interface FlowRuntimeService {


    /**
     * 流程启动执行
     *
     * @param request {@link FlowStartRequest}
     * @return {@link FlowStartResponse}
     */
    FlowStartResponse execute(FlowStartRequest<? extends FlowContentLoader> request);

}
