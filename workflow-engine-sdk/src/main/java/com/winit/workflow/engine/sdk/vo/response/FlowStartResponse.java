package com.winit.workflow.engine.sdk.vo.response;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 流程启动响应
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class FlowStartResponse extends BaseResponse {

    /**
     * 流程执行过程的生成的结果
     */
    private Map<String, Object> response = new HashMap<>();
}
