package com.winit.workflow.engine.sdk.exception;

/**
 * 工作流服务异常
 *
 * <AUTHOR>
 */
public class WorkflowException extends RuntimeException {

    public WorkflowException(String message) {
        super(message);
    }

    public WorkflowException(Exception e) {
        super(e.getMessage(), e);
    }

    public WorkflowException(String message, Exception e) {
        super(message, e);
    }

    public WorkflowException(String message, Throwable e) {
        super(message, e);
    }
}
