package com.winit.workflow.engine.sdk.service.impl;

import com.winit.workflow.engine.core.api.WorkflowDeploymentInstance;
import com.winit.workflow.engine.core.api.WorkflowEngine;
import com.winit.workflow.engine.sdk.exception.WorkflowException;
import com.winit.workflow.engine.sdk.loader.FlowContentLoader;
import com.winit.workflow.engine.sdk.service.FlowRuntimeService;
import com.winit.workflow.engine.sdk.vo.request.DeployFlowRequest;
import com.winit.workflow.engine.sdk.vo.request.FlowStartRequest;
import com.winit.workflow.engine.sdk.vo.response.FlowStartResponse;

/**
 * <AUTHOR>
 * 流程运行服务
 */
public class FlowRuntimeServiceImpl implements FlowRuntimeService {


    private final WorkflowEngine workflowEngine;

    public FlowRuntimeServiceImpl(WorkflowEngine workflowEngine) {
        if (workflowEngine == null) {
            throw new WorkflowException("workflowEngine is null");
        } else {
            this.workflowEngine = workflowEngine;
        }
    }

    @Override
    public FlowStartResponse execute(FlowStartRequest<? extends FlowContentLoader> flowStartRequest) {
        try {

            FlowStartResponse flowStartResponse = new FlowStartResponse();
            String flowContent = flowStartRequest.getFlowContent().load();
            DeployFlowRequest deployFlowRequest = new DeployFlowRequest();
            deployFlowRequest.setProcessDefinitionContent(flowContent);
            WorkflowDeploymentInstance workflowDefinition = workflowEngine.deployProcess(
                    deployFlowRequest.getDeploymentUserId(),
                    deployFlowRequest.getProcessDefinitionName(),
                    deployFlowRequest.getProcessDefinitionType(),
                    deployFlowRequest.getProcessDefinitionCode(),
                    deployFlowRequest.getProcessDefinitionId(),
                    deployFlowRequest.getProcessDefinitionVersion(),
                    deployFlowRequest.getProcessDefinitionDesc(),
                    deployFlowRequest.getProcessDefinitionContent(),
                    "");
            workflowEngine.startProcess(workflowDefinition.getProcessDefinitionId(),
                    workflowDefinition.getProcessDefinitionVersion(),
                    flowStartRequest.getVariable(),
                    flowStartResponse.getResponse(),
                    null);
            return flowStartResponse;
        } catch (RuntimeException e) {
            throw new WorkflowException(e.getMessage(), e);
        }
    }
}
