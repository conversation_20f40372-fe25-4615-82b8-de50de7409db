package com.winit.workflow.engine.sdk.delegation;

import com.alibaba.smart.framework.engine.context.ExecutionContext;
import com.winit.workflow.engine.core.provider.smartengine.AbstractExpressionJavaDelegation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 事件处理器Java委托实现
 *
 * <p>该类继承自 {@link AbstractExpressionJavaDelegation}，自动获得表达式解析能力。
 * 专注于实现事件处理的具体业务逻辑，包括Python回调处理。
 *
 * <AUTHOR>
 */
public class EventProcessorJavaDelegation extends AbstractExpressionJavaDelegation {

    private static final Logger LOGGER = LoggerFactory.getLogger(EventProcessorJavaDelegation.class);

    /**
     * Python回调处理器
     */
    private PythonCallback callback;

    /**
     * 构造函数，初始化默认的Python回调实现
     */
    public EventProcessorJavaDelegation() {
        this.callback = new PythonCallbackImpl();
    }

    @Override
    protected void doExecute(ExecutionContext executionContext, Map<String, Object> properties) {
        // 记录事件处理开始
        LOGGER.debug("[Java] 开始事件处理，请求数据: {}", executionContext.getRequest());

        // 调用Python回调处理事件
        callback.onEvent(executionContext.getRequest(), executionContext.getResponse());

        // 记录事件处理结果
        LOGGER.debug("[Java] 事件处理完成，响应数据: {}", executionContext.getResponse());

        // 保留原有的控制台输出用于调试
        System.out.println("[Java] 事件处理: " + executionContext.getRequest());
        System.out.println("[Java] 事件响应: " + executionContext.getResponse());
    }

    /**
     * 设置Python回调处理器
     *
     * @param callback Python回调处理器
     */
    public void setCallback(PythonCallback callback) {
        this.callback = callback;
    }

    /**
     * 获取Python回调处理器
     *
     * @return Python回调处理器
     */
    public PythonCallback getCallback() {
        return callback;
    }
}