package com.winit.workflow.engine.sdk.loader;

import com.winit.workflow.openapi.WorkflowAPI;
import com.winit.workflow.openapi.client.processdefinition.QueryProcessDefinitionReq;
import com.winit.workflow.openapi.client.processdefinition.QueryProcessDefinitionResp;
import com.winit.workflow.openapi.client.processdefinition.model.ProcessDefinitionInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * 从服务端加载对应的配置
 *
 * <AUTHOR>
 */
public class FlowResourceApiLoader implements FlowContentLoader {

    /**
     * 服务端地址
     */
    private final String baseUrl;

    /**
     * 读取超时时间
     */
    private final int readTimeout;

    /**
     * 连接超时时间
     */
    private final int connectTimeout;


    /**
     * 工作流API
     */
    private WorkflowAPI workflowAPI;

    /**
     * 流程编码
     */
    private String code;

    /**
     * 流程版本
     */
    private String version;

    public FlowResourceApiLoader(String baseUrl, int readTimeout, int connectTimeout) {
        this.baseUrl = baseUrl;
        this.readTimeout = readTimeout;
        this.connectTimeout = connectTimeout;
        this.workflowAPI = initClient();
    }


    public FlowContentLoader of(String code, String version) {
        this.code = code;
        this.version = version;
        return this;
    }


    @Override
    public String load() {
        if (workflowAPI == null) {
            workflowAPI = initClient();
        }

        if (code == null) {
            throw new RuntimeException("code  is null");
        }

        if (version == null) {
            throw new RuntimeException("version  is null");
        }

        QueryProcessDefinitionReq req = new QueryProcessDefinitionReq();
        ProcessDefinitionInfo processDefinitionInfo = new ProcessDefinitionInfo();
        processDefinitionInfo.setCode(code);
        processDefinitionInfo.setVersion(version);
        List<ProcessDefinitionInfo> processDefinitions = new ArrayList<>();
        processDefinitions.add(processDefinitionInfo);
        req.setProcessDefinitions(processDefinitions);
        QueryProcessDefinitionResp query = workflowAPI.processDefinitionService().query(req);

        if (query.getContents() == null || query.getContents().isEmpty()) {
            throw new RuntimeException("no process definition found");
        }

        return query.getContents().get(0).getContent();
    }

    private WorkflowAPI initClient() {
        return workflowAPI = new WorkflowAPI.Builder()
                .baseURL(baseUrl)
                .readTimeout(readTimeout)
                .connectTimeout(connectTimeout)
                .build();
    }
}
