package com.winit.workflow.engine.sdk.vo.request;

import com.winit.workflow.engine.sdk.loader.FlowContentLoader;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Map;

/**
 * 流程启动的请求
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class FlowStartRequest<T extends FlowContentLoader> extends BaseRequest {


    private T flowContent;
    /**
     * 请求参数
     */
    private Map<String, Object> variable;
}
