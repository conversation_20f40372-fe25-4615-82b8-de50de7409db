package com.winit.workflow.engine.sdk.expression.function;

import com.winit.workflow.engine.core.expression.function.ExpressionFunction;

/**
 * 计算税费函数示例
 * 演示如何实现自定义的表达式函数
 */
public class CalculateTaxFunction implements ExpressionFunction {
    
    @Override
    public String getFunctionName() {
        return "calculateTax";
    }
    
    @Override
    public String getDescription() {
        return "Calculates tax amount based on amount and rate";
    }
    
    @Override
    public int getParameterCount() {
        return 2; // 需要两个参数：金额和税率
    }
    
    @Override
    public int getPriority() {
        return 100; // 自定义函数使用默认优先级
    }
    
    @Override
    public Object execute(Object... args) {
        if (args == null || args.length != 2) {
            throw new IllegalArgumentException("calculateTax function requires exactly 2 parameters: amount and rate");
        }
        
        try {
            double amount = convertToDouble(args[0]);
            double rate = convertToDouble(args[1]);
            
            return amount * rate / 100.0;
            
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid parameters for calculateTax function: " + e.getMessage(), e);
        }
    }
    
    @Override
    public boolean validateParameters(Object... args) {
        if (args == null || args.length != 2) {
            return false;
        }
        
        try {
            convertToDouble(args[0]);
            convertToDouble(args[1]);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    private double convertToDouble(Object value) {
        if (value == null) {
            throw new IllegalArgumentException("Parameter cannot be null");
        }
        
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        
        if (value instanceof String) {
            return Double.parseDouble((String) value);
        }
        
        throw new IllegalArgumentException("Cannot convert " + value.getClass().getSimpleName() + " to double");
    }
}
