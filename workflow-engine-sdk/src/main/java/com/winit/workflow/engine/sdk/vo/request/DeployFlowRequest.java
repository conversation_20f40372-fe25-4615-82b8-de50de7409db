package com.winit.workflow.engine.sdk.vo.request;


/**
 * <AUTHOR>
 */

public class DeployFlowRequest extends BaseRequest {
    /**
     * 流程部署的用户标识
     */
    private String deploymentUserId;
    /**
     * 流程名称
     */
    private String processDefinitionName;
    /**
     * 流程类型
     */
    private String processDefinitionType;
    /**
     * 流程编码
     */
    private String processDefinitionCode;
    /**
     * 流程定义id
     */
    private String processDefinitionId;
    /**
     * 版本
     */
    private String processDefinitionVersion;
    /**
     * 描述
     */
    private String processDefinitionDesc;

    /**
     * 流程参定义
     */
    private String processDefinitionContent;

    // 手动添加getter和setter方法以确保编译通过
    public String getDeploymentUserId() {
        return deploymentUserId;
    }

    public void setDeploymentUserId(String deploymentUserId) {
        this.deploymentUserId = deploymentUserId;
    }

    public String getProcessDefinitionName() {
        return processDefinitionName;
    }

    public void setProcessDefinitionName(String processDefinitionName) {
        this.processDefinitionName = processDefinitionName;
    }

    public String getProcessDefinitionType() {
        return processDefinitionType;
    }

    public void setProcessDefinitionType(String processDefinitionType) {
        this.processDefinitionType = processDefinitionType;
    }

    public String getProcessDefinitionCode() {
        return processDefinitionCode;
    }

    public void setProcessDefinitionCode(String processDefinitionCode) {
        this.processDefinitionCode = processDefinitionCode;
    }

    public String getProcessDefinitionId() {
        return processDefinitionId;
    }

    public void setProcessDefinitionId(String processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
    }

    public String getProcessDefinitionVersion() {
        return processDefinitionVersion;
    }

    public void setProcessDefinitionVersion(String processDefinitionVersion) {
        this.processDefinitionVersion = processDefinitionVersion;
    }

    public String getProcessDefinitionDesc() {
        return processDefinitionDesc;
    }

    public void setProcessDefinitionDesc(String processDefinitionDesc) {
        this.processDefinitionDesc = processDefinitionDesc;
    }

    public String getProcessDefinitionContent() {
        return processDefinitionContent;
    }

    public void setProcessDefinitionContent(String processDefinitionContent) {
        this.processDefinitionContent = processDefinitionContent;
    }
}
