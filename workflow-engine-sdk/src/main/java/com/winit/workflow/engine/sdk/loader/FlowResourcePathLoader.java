package com.winit.workflow.engine.sdk.loader;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 资源加载
 */
public class FlowResourcePathLoader implements FlowContentLoader {
    /**
     * 资源文件路径
     */
    private final String resourcePath;

    public FlowResourcePathLoader(String resourcePath) {
        this.resourcePath = resourcePath;
    }

    @Override
    public String load() {
        try {
            // 首先尝试从类路径加载资源
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream(resourcePath);
            if (inputStream != null) {
                // 从类路径成功读取资源
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
                    return reader.lines().collect(Collectors.joining(System.lineSeparator()));
                }
            }
            
            // 如果从类路径读取失败，尝试从文件系统读取（向后兼容）
            Path path = Paths.get(resourcePath);
            List<String> lines = Files.readAllLines(path, StandardCharsets.UTF_8);
            return String.join(System.lineSeparator(), lines);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}