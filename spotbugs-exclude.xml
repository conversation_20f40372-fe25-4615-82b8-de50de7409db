<?xml version="1.0" encoding="UTF-8"?>
<FindBugsFilter>
    <!-- 忽略测试类中的一些常见问题 -->
    <Match>
        <Class name="~.*Test.*"/>
        <Bug pattern="DM_EXIT"/>
    </Match>
    
    <!-- 忽略Lombok生成的代码 -->
    <Match>
        <Bug pattern="EI_EXPOSE_REP"/>
        <Class name="~.*\.vo\..*"/>
    </Match>
    
    <Match>
        <Bug pattern="EI_EXPOSE_REP2"/>
        <Class name="~.*\.vo\..*"/>
    </Match>
    
    <!-- 忽略序列化相关的警告，对于VO类 -->
    <Match>
        <Bug pattern="SE_NO_SERIALVERSIONID"/>
        <Class name="~.*\.vo\..*"/>
    </Match>
    
    <!-- 忽略Spring框架相关的注入问题 -->
    <Match>
        <Bug pattern="UWF_FIELD_NOT_INITIALIZED_IN_CONSTRUCTOR"/>
        <Field annotation="org.springframework.beans.factory.annotation.Autowired"/>
    </Match>
    
    <!-- 忽略一些常见的性能问题（对于配置类等） -->
    <Match>
        <Bug pattern="DMI_CONSTANT_DB_PASSWORD"/>
        <Class name="~.*Configuration.*"/>
    </Match>
    
    <!-- 忽略未使用的字段警告，对于某些特定类 -->
    <Match>
        <Bug pattern="URF_UNREAD_FIELD"/>
        <Class name="~.*Adapter.*"/>
    </Match>
    
    <!-- 忽略异常捕获问题，对于委托类 -->
    <Match>
        <Bug pattern="REC_CATCH_EXCEPTION"/>
        <Class name="~.*Delegation.*"/>
    </Match>
</FindBugsFilter> 