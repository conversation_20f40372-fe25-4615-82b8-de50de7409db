package com.winit.workflow.engine.core.provider.smartengine;


import com.alibaba.smart.framework.engine.SmartEngine;
import com.alibaba.smart.framework.engine.bpmn.constant.BpmnNameSpaceConstant;
import com.alibaba.smart.framework.engine.common.util.MapUtil;
import com.alibaba.smart.framework.engine.configuration.InstanceAccessor;
import com.alibaba.smart.framework.engine.configuration.ProcessEngineConfiguration;
import com.alibaba.smart.framework.engine.configuration.impl.DefaultInstanceAccessor;
import com.alibaba.smart.framework.engine.configuration.impl.DefaultProcessEngineConfiguration;
import com.alibaba.smart.framework.engine.configuration.impl.DefaultSmartEngine;
import com.alibaba.smart.framework.engine.constant.DeploymentStatusConstant;
import com.alibaba.smart.framework.engine.constant.LogicStatusConstant;
import com.alibaba.smart.framework.engine.constant.SmartBase;
import com.alibaba.smart.framework.engine.extension.scanner.SimpleAnnotationScanner;
import com.alibaba.smart.framework.engine.instance.impl.DefaultDeploymentInstance;
import com.alibaba.smart.framework.engine.model.assembly.ProcessDefinition;
import com.alibaba.smart.framework.engine.model.instance.InstanceStatus;
import com.alibaba.smart.framework.engine.model.instance.ProcessInstance;
import com.alibaba.smart.framework.engine.persister.custom.session.PersisterSession;
import com.alibaba.smart.framework.engine.service.command.ProcessCommandService;
import com.alibaba.smart.framework.engine.service.command.RepositoryCommandService;
import com.winit.workflow.engine.core.api.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SmartEngine适配器实现
 */
public class SmartEngineAdapter implements ConfigurableWorkflowEngine {

    private WorkflowEngineConfig<ProcessEngineConfiguration> workflowEngineConfig;

    private ProcessCommandService processCommandService;

    private RepositoryCommandService repositoryCommandService;


    public SmartEngineAdapter() {
        this(new SmartEngineConfiguration(new DefaultInstanceAccessor()));
    }

    public SmartEngineAdapter(InstanceAccessor smartEngineInstanceAccessor) {
        this(new SmartEngineConfiguration(smartEngineInstanceAccessor));
    }

    public SmartEngineAdapter(WorkflowEngineConfig<ProcessEngineConfiguration> workflowEngineConfig) {
        this.workflowEngineConfig = workflowEngineConfig;
        SmartEngine smartEngine = init();
        this.processCommandService = smartEngine.getProcessCommandService();
        this.repositoryCommandService = smartEngine.getRepositoryCommandService();
    }

    /**
     * 初始化SmartEngine
     *
     * @return SmartEngine
     */
    private SmartEngine init() {
        ProcessEngineConfiguration processEngineConfiguration = initProcessEngineConfiguration();
        DefaultSmartEngine defaultSmartEngine = new DefaultSmartEngine();
        defaultSmartEngine.init(processEngineConfiguration);
        return defaultSmartEngine;
    }
    /**
     * 初始化ProcessEngineConfiguration
     *
     * @return ProcessEngineConfiguration
     */
    private ProcessEngineConfiguration initProcessEngineConfiguration() {
        if (this.workflowEngineConfig == null) {
            throw new RuntimeException("WorkflowEngineConfig not found");
        }
        ProcessEngineConfiguration processEngineConfiguration = this.workflowEngineConfig.initWorkflowEngineConfig();
        if (processEngineConfiguration == null) {
            throw new RuntimeException("ProcessEngineConfiguration not found");
        }

        return processEngineConfiguration;
    }

    private void buildDefaultSupportNameSpace(ProcessEngineConfiguration processEngineConfiguration) {
        Map<String, Object> magicExtension = MapUtil.newHashMap();

        Map<String, String> tuples = new HashMap<String, String>();
        tuples.put(SmartBase.SMART_NS, "");

        //兼容主流开源的 namespace，便于兼容他们配套的前端设计器
        tuples.put(BpmnNameSpaceConstant.CAMUNDA_NAME_SPACE, "smart");
        tuples.put(BpmnNameSpaceConstant.FLOWABLE_NAME_SPACE, "flowable");
        tuples.put(BpmnNameSpaceConstant.ACTIVITI_NAME_SPACE, "activiti");

        magicExtension.put("fallBack", tuples);

        processEngineConfiguration.setMagicExtension(magicExtension);
    }


    @Override
    public WorkflowDeploymentInstance deployProcess(String deploymentUserId,
                                                    String processDefinitionName,
                                                    String processDefinitionType,
                                                    String processDefinitionCode,
                                                    String processDefinitionId,
                                                    String processDefinitionVersion,
                                                    String processDefinitionDesc,
                                                    String processDefinitionContent,
                                                    String tenantId) {
        // 实现SmartEngine的部署逻辑
        ProcessDefinition firstProcessDefinition = this.repositoryCommandService
                .deployWithUTF8Content(processDefinitionContent)
                .getFirstProcessDefinition();
        DefaultDeploymentInstance deploymentInstance = new DefaultDeploymentInstance();
        deploymentInstance.setDeploymentStatus(DeploymentStatusConstant.ACTIVE);
        deploymentInstance.setLogicStatus(LogicStatusConstant.VALID);
        deploymentInstance.setProcessDefinitionContent(processDefinitionContent);
        deploymentInstance.setProcessDefinitionVersion(firstProcessDefinition.getVersion());
        deploymentInstance.setProcessDefinitionName(firstProcessDefinition.getName());
        deploymentInstance.setProcessDefinitionId(firstProcessDefinition.getId());

        return new SmartEngineWorkflowDeploymentInstance(deploymentInstance);
    }

    @Override
    public void uninstallProcess(String processDefinitionId, String processDefinitionType, String processDefinitionCode, String tenantId) {

    }

    @Override
    public WorkflowInstance startProcess(String processDefinitionId,
                               String processDefinitionVersion,
                               Map<String, Object> variables,
                                         Map<String, Object> response,
                                         String tenantId) {
        try {
            //Custom 模式必须在 try,finally块中使用 PersisterSession.create()和 PersisterSession.destroySession() 。
            // SmartEngine 内部执行时，会依赖该Session，从该session获取流程实例数据
            PersisterSession.create();

            ProcessInstance processInstance = processCommandService.start(
                    processDefinitionId,
                    processDefinitionVersion,
                    variables,
                    response
            );

            if (processInstance == null) {
                throw new RuntimeException("Process instance not found");
            }

            if (!processInstance.getStatus().equals(InstanceStatus.completed)) {
                throw new RuntimeException(
                        String.format("Process instance %s version %s not completed, request param %s",
                                processDefinitionId, processDefinitionVersion, variables)
                );
            }

            WorkflowInstance workflowInstance = new SmartEngineWorkflowInstance(processInstance);
            return workflowInstance;
        } finally {
            PersisterSession.destroySession();
        }
    }

    @Override
    public WorkflowInstance completeTask(String taskInstanceId, String taskInstanceType, Map<String, Object> variables, String tenantId) {
        throw new UnsupportedOperationException();
    }

    @Override
    public WorkflowInstance getProcessInstance(String processInstanceId, String tenantId) {
        throw new UnsupportedOperationException();
    }

    @Override
    public List<WorkflowTaskInstance<?>> getTasks(String processInstanceId, Map<String, Object> variables, String tenantId) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void loadDeployedFlows() {
        throw new UnsupportedOperationException();
    }

    @Override
    public void configure(WorkflowEngineConfig<?> config) {
        this.workflowEngineConfig = (WorkflowEngineConfig<ProcessEngineConfiguration>) config;
        SmartEngine smartEngine = init();
        this.processCommandService = smartEngine.getProcessCommandService();
        this.repositoryCommandService = smartEngine.getRepositoryCommandService();
    }


    /**
     * SmartEngine配置
     */
    public static class SmartEngineConfiguration implements SmartEngineConfig {

        private final InstanceAccessor smartEngineInstanceAccessor;

        public SmartEngineConfiguration(InstanceAccessor smartEngineInstanceAccessor) {
            this.smartEngineInstanceAccessor = smartEngineInstanceAccessor;
        }

        @Override
        public ProcessEngineConfiguration initWorkflowEngineConfig() {

            DefaultProcessEngineConfiguration defaultProcessEngineConfiguration = new DefaultProcessEngineConfiguration();

            defaultProcessEngineConfiguration.setInstanceAccessor(smartEngineInstanceAccessor);
            defaultProcessEngineConfiguration.setIdGenerator(new TimeBasedIdGenerator());
            defaultProcessEngineConfiguration.setAnnotationScanner(new SimpleAnnotationScanner(
                    SmartEngine.class.getPackage().getName(),
                    "com.winit.workflow.agent"
            ));


            return defaultProcessEngineConfiguration;
        }
    }

} 