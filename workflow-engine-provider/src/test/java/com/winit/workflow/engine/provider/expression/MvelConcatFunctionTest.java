package com.winit.workflow.engine.provider.expression;

import com.winit.workflow.engine.core.expression.provider.MvelExpressionEngine;
import org.junit.Before;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;

public class MvelConcatFunctionTest {

    private MvelExpressionEngine expressionEngine;
    
    @Before
    public void setUp() {
        expressionEngine = new MvelExpressionEngine();
        
        // 注册concat函数
        expressionEngine.registerFunction("concat", new java.util.function.Function<Object[], String>() {
            @Override
            public String apply(Object[] args) {
                if (args == null) return "";
                
                StringBuilder sb = new StringBuilder();
                for (Object arg : args) {
                    if (arg != null) {
                        sb.append(arg);
                    }
                }
                return sb.toString();
            }
        });
    }
    
    @Test
    public void testConcatFunction() {
        Map<String, Object> context = new HashMap<>();
        context.put("name", "张三");
        
        Object result = expressionEngine.evaluate("${concat('Hello, ', name)}", context);
        assertEquals("Hello, 张三", result);
    }
    
    @Test
    public void testNestedObject() {
        Map<String, Object> user = new HashMap<>();
        user.put("name", "李四");
        
        Map<String, Object> context = new HashMap<>();
        context.put("user", user);
        
        Object result = expressionEngine.evaluate("${concat('Hello, ', user.name)}", context);
        assertEquals("Hello, 李四", result);
    }
    
    @Test
    public void testMultipleParams() {
        Map<String, Object> context = new HashMap<>();
        context.put("firstName", "张");
        context.put("lastName", "三");
        
        Object result = expressionEngine.evaluate("${concat(firstName, ' ', lastName, '先生')}", context);
        assertEquals("张 三先生", result);
    }
}