package com.winit.workflow.engine.core.expression.function.builtin;

import com.winit.workflow.engine.core.expression.function.ExpressionFunction;

/**
 * 字符串连接函数
 * 将多个参数连接成一个字符串
 */
public class ConcatFunction implements ExpressionFunction {
    
    @Override
    public String getFunctionName() {
        return "concat";
    }
    
    @Override
    public String getDescription() {
        return "Concatenates multiple values into a single string";
    }
    
    @Override
    public int getParameterCount() {
        return -1; // 支持可变参数
    }
    
    @Override
    public int getPriority() {
        return 10; // 内置函数使用较高优先级
    }
    
    @Override
    public Object execute(Object... args) {
        if (args == null || args.length == 0) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        for (Object arg : args) {
            if (arg != null) {
                sb.append(arg);
            }
        }
        
        return sb.toString();
    }
    
    @Override
    public boolean validateParameters(Object... args) {
        // concat函数接受任意数量的参数，包括0个
        return true;
    }

    @Override
    public String getExample() {
        return "${concat('Hello', ' ', 'World')}\n// Result: \"Hello World\"\n\n${concat('User: ', username, ', Age: ', age)}\n// Result: \"User: John, Age: 25\"";
    }

    @Override
    public String getFunctionSignature() {
        return "concat(value1, value2, ...): concatenates multiple values into a single string\nParameters: value1, value2, ... - values to concatenate (variable arguments)";
    }
}
