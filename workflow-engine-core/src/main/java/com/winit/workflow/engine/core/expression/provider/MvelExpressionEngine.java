package com.winit.workflow.engine.core.expression.provider;

import com.winit.workflow.engine.core.expression.ExpressionEngine;
import com.winit.workflow.engine.core.expression.function.ExpressionFunction;
import com.winit.workflow.engine.core.expression.function.FunctionRegistry;
import com.winit.workflow.engine.core.expression.function.DefaultFunctionRegistry;
import com.winit.workflow.engine.core.expression.function.FunctionDiscovery;
import org.mvel2.MVEL;
import org.mvel2.integration.impl.MapVariableResolverFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 基于MVEL的表达式引擎实现
 * 优化版本，修复了函数注册和调用机制
 */
public class MvelExpressionEngine implements ExpressionEngine {
    private static final Logger LOGGER = LoggerFactory.getLogger(MvelExpressionEngine.class);
    private static final Pattern EXPRESSION_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");

    // 缓存已编译的表达式
    private final Map<String, Serializable> compiledExpressions = new ConcurrentHashMap<>();

    // 存储注册的函数
    private final Map<String, Object> functionMap = new HashMap<>();

    // 函数注册器
    private final FunctionRegistry functionRegistry;

    // 缓存函数替换规则，避免重复计算
    private volatile String cachedFunctionReplacementRules = null;
    private volatile long lastFunctionRegistryUpdate = 0;

    public MvelExpressionEngine() {
        LOGGER.info("Initializing MvelExpressionEngine");

        // 初始化函数注册器
        this.functionRegistry = new DefaultFunctionRegistry();

        // 自动发现和注册函数
        autoRegisterFunctions();

        // 生成动态函数方法
        generateDynamicFunctionMethods();
    }

    /**
     * 静态函数注册表，用于存储全局函数
     */
    private static final Map<String, ExpressionFunction> GLOBAL_FUNCTIONS = new ConcurrentHashMap<>();

    /**
     * 动态函数调用器，支持所有注册的ExpressionFunction
     * 使用通用invoke方法实现真正的动态函数调用
     */
    public static class Functions {

        /**
         * 通用函数调用方法，支持所有注册的函数
         *
         * @param functionName 函数名称
         * @param args 函数参数
         * @return 函数执行结果
         */
        public static Object invoke(String functionName, Object... args) {
            ExpressionFunction function = GLOBAL_FUNCTIONS.get(functionName);
            if (function != null) {
                try {
                    // 验证参数
                    if (!function.validateParameters(args)) {
                        LOGGER.warn("Invalid parameters for function '{}': {}", functionName,
                            java.util.Arrays.toString(args));
                    }

                    Object result = function.execute(args);
                    LOGGER.debug("Function '{}' executed successfully with result: {}", functionName, result);
                    return result;

                } catch (Exception e) {
                    LOGGER.error("Error executing function '{}' with args: {}", functionName,
                        java.util.Arrays.toString(args), e);
                    throw new RuntimeException("Function execution failed: " + functionName, e);
                }
            } else {
                throw new IllegalArgumentException("Unknown function: " + functionName);
            }
        }

        // 内置函数的静态方法实现（保持向后兼容）
        public static Object concat(Object... args) {
            return invoke("concat", args);
        }

        public static Object uppercase(Object arg) {
            return invoke("uppercase", arg);
        }

        public static Object lowercase(Object arg) {
            return invoke("lowercase", arg);
        }

        public static Object now() {
            return invoke("now");
        }

        // 自定义函数的静态方法实现（示例）
        public static Object calculateTax(Object amount, Object rate) {
            return invoke("calculateTax", amount, rate);
        }

        public static Object generateOrderNumber() {
            return invoke("generateOrderNumber");
        }
    }

    @Override
    public boolean isExpression(String value) {
        if (value == null) {
            return false;
        }
        return EXPRESSION_PATTERN.matcher(value).find();
    }

    @Override
    public Object evaluate(String expression, Map<String, Object> context) {
        if (expression == null) {
            return null;
        }

        try {
            LOGGER.debug("Evaluating expression: {}", expression);

            // 检查是否包含表达式
            Matcher matcher = EXPRESSION_PATTERN.matcher(expression);
            if (!matcher.find()) {
                return expression;
            }

            // 重置匹配器以便重新使用
            matcher.reset();

            // 如果是纯表达式（形如${...}），直接解析内容
            if (expression.startsWith("${") && expression.endsWith("}")
                    && expression.indexOf("${") == expression.lastIndexOf("${")) {

                String expressionContent = expression.substring(2, expression.length() - 1);
                return evaluateContent(expressionContent, context);
            }

            // 处理包含多个表达式或混合文本的情况
            StringBuffer result = new StringBuffer();
            while (matcher.find()) {
                String expressionContent = matcher.group(1);
                Object value = evaluateContent(expressionContent, context);

                // 将null转换为空字符串
                String replacement = (value == null) ? "" : value.toString();

                // 处理特殊字符
                replacement = replacement.replace("\\", "\\\\").replace("$", "\\$");

                matcher.appendReplacement(result, replacement);
            }
            matcher.appendTail(result);

            return result.toString();
        } catch (Exception e) {
            LOGGER.error("Failed to evaluate expression: {}", expression, e);
            throw new RuntimeException("Expression evaluation failed: " + expression, e);
        }
    }

    /**
     * 解析表达式内容
     */
    private Object evaluateContent(String expressionContent, Map<String, Object> context) {
        try {
            LOGGER.debug("Evaluating expression content: {}", expressionContent);

            // 创建MVEL上下文
            Map<String, Object> fullContext = new HashMap<>();
            if (context != null) {
                fullContext.putAll(context);
            }

            // 创建MVEL变量解析器
            MapVariableResolverFactory factory = new MapVariableResolverFactory(fullContext);

            // 动态替换函数调用为静态方法调用
            String modifiedExpression = applyDynamicFunctionReplacement(expressionContent);

            LOGGER.debug("Modified expression: {}", modifiedExpression);

            // 获取或编译表达式
            Serializable compiled = compiledExpressions.computeIfAbsent(
                    modifiedExpression, MVEL::compileExpression);

            // 执行表达式
            Object result = MVEL.executeExpression(compiled, factory);
            LOGGER.debug("Expression result: {}", result);

            return result;
        } catch (Exception e) {
            LOGGER.error("Failed to evaluate expression content: {}", expressionContent, e);
            throw e;
        }
    }

    @Override
    public void registerFunction(String name, Object function) {
        LOGGER.info("Registering legacy function: {}", name);
        functionMap.put(name, function);

        // 为了兼容性，将传统函数包装为ExpressionFunction
        ExpressionFunction wrappedFunction = new LegacyFunctionWrapper(name, function);
        functionRegistry.registerFunction(wrappedFunction);
        GLOBAL_FUNCTIONS.put(name, wrappedFunction);

        // 清除缓存，因为新函数已注册
        invalidateFunctionReplacementCache();
    }

    /**
     * 注册ExpressionFunction
     */
    public void registerExpressionFunction(ExpressionFunction function) {
        LOGGER.info("Registering expression function: {}", function.getFunctionName());
        functionRegistry.registerFunction(function);
        GLOBAL_FUNCTIONS.put(function.getFunctionName(), function);

        // 清除缓存，因为新函数已注册
        invalidateFunctionReplacementCache();
    }

    /**
     * 获取函数注册器
     *
     * @return 函数注册器实例
     */
    public FunctionRegistry getFunctionRegistry() {
        return functionRegistry;
    }

    /**
     * 自动注册函数
     */
    private void autoRegisterFunctions() {
        LOGGER.info("Starting automatic function registration");

        try {
            int count = FunctionDiscovery.discoverAndRegister(functionRegistry);

            // 将注册的函数同步到全局函数表
            Map<String, ExpressionFunction> allFunctions = functionRegistry.getAllFunctions();
            GLOBAL_FUNCTIONS.putAll(allFunctions);

            LOGGER.info("Automatic function registration completed. Registered {} functions", count);

        } catch (Exception e) {
            LOGGER.error("Error during automatic function registration", e);
        }

        // 清除缓存的替换规则，强制重新生成
        invalidateFunctionReplacementCache();
    }

    /**
     * 动态应用函数替换规则
     *
     * @param expressionContent 原始表达式内容
     * @return 替换后的表达式内容
     */
    private String applyDynamicFunctionReplacement(String expressionContent) {
        String modifiedExpression = expressionContent;

        // 获取所有已注册的函数名称
        Map<String, ExpressionFunction> allFunctions = functionRegistry.getAllFunctions();

        // 为每个注册的函数应用替换规则，使用通用的invoke方法
        for (String functionName : allFunctions.keySet()) {
            String pattern = "\\b" + functionName + "\\(";
            String replacement = "com.winit.workflow.engine.core.expression.provider.MvelExpressionEngine.Functions.invoke(\""
                + functionName + "\", ";
            modifiedExpression = modifiedExpression.replaceAll(pattern, replacement);
        }

        LOGGER.debug("Applied dynamic function replacement. Original: {}, Modified: {}",
            expressionContent, modifiedExpression);

        return modifiedExpression;
    }

    /**
     * 清除函数替换规则缓存
     */
    private void invalidateFunctionReplacementCache() {
        cachedFunctionReplacementRules = null;
        lastFunctionRegistryUpdate = System.currentTimeMillis();
        LOGGER.debug("Function replacement cache invalidated");
    }

    /**
     * 生成动态函数方法
     * 为所有注册的函数在Functions类中创建对应的静态方法
     */
    private void generateDynamicFunctionMethods() {
        Map<String, ExpressionFunction> allFunctions = functionRegistry.getAllFunctions();

        LOGGER.info("Generating dynamic function methods for {} functions", allFunctions.size());

        for (String functionName : allFunctions.keySet()) {
            try {
                // 检查是否已经存在对应的静态方法
                if (!hasStaticMethod(functionName)) {
                    LOGGER.debug("Function '{}' will use dynamic execution", functionName);
                }
            } catch (Exception e) {
                LOGGER.warn("Error checking static method for function '{}': {}", functionName, e.getMessage());
            }
        }

        LOGGER.info("Dynamic function method generation completed");
    }

    /**
     * 检查Functions类是否已有指定名称的静态方法
     */
    private boolean hasStaticMethod(String methodName) {
        try {
            // 尝试获取方法，如果存在则返回true
            Functions.class.getMethod(methodName, Object[].class);
            return true;
        } catch (NoSuchMethodException e) {
            // 尝试其他可能的方法签名
            try {
                Functions.class.getMethod(methodName);
                return true;
            } catch (NoSuchMethodException e2) {
                try {
                    Functions.class.getMethod(methodName, Object.class);
                    return true;
                } catch (NoSuchMethodException e3) {
                    try {
                        Functions.class.getMethod(methodName, Object.class, Object.class);
                        return true;
                    } catch (NoSuchMethodException e4) {
                        return false;
                    }
                }
            }
        }
    }

    /**
     * 传统函数包装器，用于将传统的Function/Supplier等包装为ExpressionFunction
     */
    private static class LegacyFunctionWrapper implements ExpressionFunction {
        private final String name;
        private final Object function;

        public LegacyFunctionWrapper(String name, Object function) {
            this.name = name;
            this.function = function;
        }

        @Override
        public String getFunctionName() {
            return name;
        }

        @Override
        public String getDescription() {
            return "Legacy function wrapper for: " + name;
        }

        @Override
        public int getPriority() {
            return 50; // 中等优先级
        }

        @Override
        @SuppressWarnings("unchecked")
        public Object execute(Object... args) {
            if (function instanceof Function) {
                if (args == null || args.length == 0) {
                    return ((Function<Object, Object>) function).apply(null);
                } else if (args.length == 1) {
                    return ((Function<Object, Object>) function).apply(args[0]);
                } else {
                    return ((Function<Object[], Object>) function).apply(args);
                }
            } else if (function instanceof Supplier) {
                return ((Supplier<Object>) function).get();
            } else {
                throw new UnsupportedOperationException("Unsupported function type: " +
                    function.getClass().getName());
            }
        }
    }
}