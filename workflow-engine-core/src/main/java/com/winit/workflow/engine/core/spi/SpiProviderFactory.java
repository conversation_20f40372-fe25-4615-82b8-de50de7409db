package com.winit.workflow.engine.core.spi;

import com.winit.workflow.engine.core.provider.defaultengine.DefaultEngineProvider;

import java.util.Iterator;
import java.util.ServiceLoader;

/**
 * SPI加载器，用于加载WorkflowEngineProvider的实现
 *
 * <AUTHOR>
 */
public class SpiProviderFactory {

    /**
     * 获取WorkflowEngineProvider的实现
     * 如果找到多个实现，抛出异常
     * 如果没有找到实现，使用默认的DefaultEngineProvider
     *
     * @return WorkflowEngineProvider的实现
     */
    public static WorkflowEngineProvider getProvider() {
        ServiceLoader<WorkflowEngineProvider> serviceLoader = ServiceLoader.load(WorkflowEngineProvider.class);
        Iterator<WorkflowEngineProvider> iterator = serviceLoader.iterator();

        if (!iterator.hasNext()) {
            // 如果没有找到实现，使用默认的DefaultEngineProvider
            return DefaultEngineProvider.getInstance();
        }

        WorkflowEngineProvider provider = iterator.next();

        if (iterator.hasNext()) {
            // 如果找到多个实现，抛出异常
            throw new IllegalStateException("Found more than one WorkflowEngineProvider implementation");
        }

        return provider;
    }
}
