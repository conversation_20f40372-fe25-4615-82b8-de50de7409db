package com.winit.workflow.engine.core.api;

import java.util.Iterator;
import java.util.ServiceLoader;

/**
 * SPI加载器，用于加载WorkflowEngineProvider的实现
 *
 * <AUTHOR>
 */
public class WorkflowEngineFactory {

    /**
     * 获取WorkflowEngineProvider的实现
     * 如果找到多个实现，抛出异常
     * 如果没有找到实现，使用默认的WorkflowEngine
     *
     * @return WorkflowEngineProvider的实现
     */
    public static WorkflowEngine getEngine() {
        return getEngine(null);
    }

    /**
     * 获取WorkflowEngineProvider的实现，并使用指定配置初始化
     * 如果找到多个实现，抛出异常
     * 如果没有找到实现，使用默认的DefaultEngineProvider
     *
     * @param config 引擎配置参数
     * @return WorkflowEngineProvider的实现
     */
    public static WorkflowEngine getEngine(WorkflowEngineConfig<?> config) {
        // 使用SPI机制加载WorkflowEngine实现
        ServiceLoader<WorkflowEngine> serviceLoader = ServiceLoader.load(WorkflowEngine.class);
        Iterator<WorkflowEngine> iterator = serviceLoader.iterator();

        if (!iterator.hasNext()) {
            // 如果没有找到实现，尝试通过WorkflowEngineProvider创建
            return createEngineFromProvider(config);
        }

        WorkflowEngine engine = iterator.next();

        if (iterator.hasNext()) {
            // 如果找到多个实现，抛出异常
            throw new IllegalStateException("Found more than one WorkflowEngine implementation");
        }

        // 如果有配置参数且引擎支持配置，则进行配置
        if (config != null && engine instanceof ConfigurableWorkflowEngine) {
            ((ConfigurableWorkflowEngine) engine).configure(config);
        }

        return engine;
    }

    /**
     * 通过WorkflowEngineProvider创建引擎
     *
     * @param config 引擎配置参数
     * @return 创建的WorkflowEngine实例
     */
    private static WorkflowEngine createEngineFromProvider(WorkflowEngineConfig<?> config) {
        // 使用SpiProviderFactory获取Provider
        try {
            com.winit.workflow.engine.core.spi.WorkflowEngineProvider provider = 
                com.winit.workflow.engine.core.spi.SpiProviderFactory.getProvider();
            return provider.createEngine(config);
        } catch (Exception e) {
            throw new IllegalStateException("No WorkflowEngine implementation found and failed to create from provider", e);
        }
    }
}
