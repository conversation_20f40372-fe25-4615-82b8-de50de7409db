package com.winit.workflow.engine.core.provider.defaultengine;

import com.winit.workflow.engine.core.api.WorkflowEngine;
import com.winit.workflow.engine.core.api.WorkflowEngineConfig;
import com.winit.workflow.engine.core.spi.WorkflowEngineProvider;

/**
 * 默认的引擎提供者实现
 * 作为防腐层，当没有其他工作流引擎实现时提供明确的错误提示
 *
 * <AUTHOR>
 */
public class DefaultEngineProvider implements WorkflowEngineProvider {

    private static volatile DefaultEngineProvider instance;

    public static DefaultEngineProvider getInstance() {
        if (instance == null) {
            synchronized (DefaultEngineProvider.class) {
                if (instance == null) {
                    instance = new DefaultEngineProvider();
                }
            }
        }
        return instance;
    }

    @Override
    public String getType() {
        return "default";
    }

    @Override
    public WorkflowEngine createEngine(WorkflowEngineConfig<?> config) {
        // 作为防腐层，不提供实际的工作流引擎实现
        // 而是抛出明确的异常，指导用户正确配置SmartEngine
        throw new UnsupportedOperationException(
                "DefaultEngineProvider is a fallback provider and does not provide actual workflow engine implementation. " +
                        "Please ensure SmartEngine is properly configured and available in the classpath."
        );
    }

    @Override
    public String getDescription() {
        return "Default workflow engine provider (fallback only)";
    }
}
