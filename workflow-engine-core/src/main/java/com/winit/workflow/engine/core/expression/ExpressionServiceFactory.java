package com.winit.workflow.engine.core.expression;

import com.winit.workflow.engine.core.expression.provider.MvelExpressionEngine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 表达式服务工厂类
 */
public class ExpressionServiceFactory {
    private static final Logger LOGGER = LoggerFactory.getLogger(ExpressionServiceFactory.class);

    private static volatile ExpressionService INSTANCE;

    /**
     * 获取表达式服务实例
     *
     * @return 表达式服务实例
     */
    public static ExpressionService getInstance() {
        if (INSTANCE == null) {
            synchronized (ExpressionServiceFactory.class) {
                if (INSTANCE == null) {
                    LOGGER.info("Creating new ExpressionService instance");

                    // 创建表达式引擎
                    ExpressionEngine engine = createExpressionEngine();

                    // 创建表达式服务
                    INSTANCE = new DefaultExpressionService(engine);

                    LOGGER.info("ExpressionService instance created successfully");
                }
            }
        }
        return INSTANCE;
    }

    /**
     * 创建表达式引擎
     */
    private static ExpressionEngine createExpressionEngine() {
        LOGGER.info("Creating new MvelExpressionEngine");
        return new MvelExpressionEngine();
    }

    /**
     * 重置表达式服务实例（主要用于测试）
     */
    public static void reset() {
        synchronized (ExpressionServiceFactory.class) {
            LOGGER.info("Resetting ExpressionService instance");
            INSTANCE = null;
        }
    }
}