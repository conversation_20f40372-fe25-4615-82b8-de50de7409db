package com.winit.workflow.engine.core.provider.smartengine;

import com.alibaba.smart.framework.engine.configuration.IdGenerator;
import com.alibaba.smart.framework.engine.model.instance.Instance;

import java.util.concurrent.atomic.AtomicLong;

class TimeBasedIdGenerator implements IdGenerator {

    private final AtomicLong temp = new AtomicLong(0);

    @Override
    public void generate(Instance instance) {
        String s = System.currentTimeMillis() + temp.getAndIncrement() + "";
        instance.setInstanceId(s);
    }
}