package com.winit.workflow.engine.core.provider.smartengine;

import com.alibaba.smart.framework.engine.model.assembly.ProcessDefinition;
import com.winit.workflow.engine.core.api.WorkflowDefinition;


public class SmartEngineWorkflowDefinition implements WorkflowDefinition {

    private final ProcessDefinition processDefinition;

    public SmartEngineWorkflowDefinition(ProcessDefinition processDefinition) {
        this.processDefinition = processDefinition;
    }

    @Override
    public String getName() {
        return processDefinition.getName();
    }

    @Override
    public String getVersion() {
        return processDefinition.getVersion();
    }

    @Override
    public String getId() {
        return processDefinition.getId();
    }
}
