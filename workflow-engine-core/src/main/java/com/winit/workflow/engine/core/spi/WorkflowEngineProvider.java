package com.winit.workflow.engine.core.spi;


import com.winit.workflow.engine.core.api.WorkflowEngine;
import com.winit.workflow.engine.core.api.WorkflowEngineConfig;

/**
 * 工作流引擎提供者接口
 * 用于创建具体的工作流引擎实现
 */
public interface WorkflowEngineProvider {

    /**
     * 获取引擎类型
     *
     * @return 引擎类型
     */
    String getType();

    /**
     * 创建引擎实例
     *
     * @param config 引擎配置
     * @return 工作流引擎实例
     */
    WorkflowEngine createEngine(WorkflowEngineConfig<?> config);

    /**
     * 获取引擎描述
     *
     * @return 引擎描述
     */
    String getDescription();
} 