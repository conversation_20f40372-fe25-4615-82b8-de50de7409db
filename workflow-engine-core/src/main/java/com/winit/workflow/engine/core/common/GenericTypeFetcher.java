package com.winit.workflow.engine.core.common;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @param <T>
 *
 */
public abstract class GenericTypeFetcher<T> {
    private final Type type;

    public GenericTypeFetcher() {
        this.type = ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }

    public Type getType() {
        return this.type;
    }
}