package com.winit.workflow.engine.core.expression.function.custom;

import com.winit.workflow.engine.core.expression.function.ExpressionFunction;

/**
 * String contains function
 * Checks if a string contains another string (case-sensitive)
 */
public class ContainsFunction implements ExpressionFunction {
    
    @Override
    public String getFunctionName() {
        return "contains";
    }
    
    @Override
    public String getDescription() {
        return "Checks if a string contains another string (case-sensitive)";
    }
    
    @Override
    public int getParameterCount() {
        return 2;
    }
    
    @Override
    public int getPriority() {
        return 200; // Custom function priority
    }
    
    @Override
    public Object execute(Object... args) {
        if (args == null || args.length != 2) {
            throw new IllegalArgumentException("contains function requires two arguments");
        }
        
        Object textArg = args[0];
        Object searchArg = args[1];
        
        if (textArg == null || searchArg == null) {
            return false;
        }
        
        String text = textArg.toString();
        String searchText = searchArg.toString();
        
        return text.contains(searchText);
    }
    
    @Override
    public boolean validateParameters(Object... args) {
        return args != null && args.length == 2;
    }
    
    @Override
    public String getExample() {
        return "${contains('Hello World', 'World')}\n// Result: true\n\n${contains('Hello World', 'world')}\n// Result: false (case-sensitive)\n\n${contains(text, searchTerm)}\n// Checks if text contains searchTerm";
    }
    
    @Override
    public String getFunctionSignature() {
        return "contains(text, searchText): checks if text contains searchText\nParameters: text - the string to search in, searchText - the string to search for";
    }
}
