package com.winit.workflow.engine.core.expression;

import com.winit.workflow.engine.core.expression.function.ExpressionFunction;

/**
 * 函数工具类
 * 提供函数类型判断等实用方法
 *
 * <AUTHOR>
 */
public class FunctionExampleGenerator {

    /**
     * 判断函数类型
     *
     * @param function 表达式函数
     * @return 函数类型字符串
     */
    public static String getFunctionType(ExpressionFunction function) {
        if (function == null) {
            return "unknown";
        }

        String className = function.getClass().getName();

        // 判断是否为内置函数
        if (className.contains(".builtin.")) {
            return "builtin";
        }

        // 判断优先级，内置函数通常使用较高优先级（较小的数值）
        if (function.getPriority() <= 50) {
            return "builtin";
        }

        return "custom";
    }
}
