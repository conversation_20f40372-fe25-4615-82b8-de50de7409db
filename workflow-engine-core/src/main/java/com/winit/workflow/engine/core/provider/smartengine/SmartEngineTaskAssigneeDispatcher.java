package com.winit.workflow.engine.core.provider.smartengine;

import com.alibaba.smart.framework.engine.configuration.InstanceAccessor;
import com.alibaba.smart.framework.engine.configuration.TaskAssigneeDispatcher;
import com.alibaba.smart.framework.engine.context.ExecutionContext;
import com.alibaba.smart.framework.engine.model.assembly.Activity;
import com.alibaba.smart.framework.engine.model.instance.TaskAssigneeCandidateInstance;
import com.winit.workflow.engine.core.api.WorkflowTaskAssigneeDispatcher;
import com.winit.workflow.engine.core.api.model.WorkflowTaskAssigneeCandidateInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.lang.reflect.Modifier;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

/**
 * SmartEngine任务分配者调度器实现
 * 通过扫描WorkflowTaskAssigneeDispatcher接口的实现来进行任务分配
 *
 * <AUTHOR>
 */
public class SmartEngineTaskAssigneeDispatcher implements TaskAssigneeDispatcher {

    private static final Logger LOGGER = LoggerFactory.getLogger(SmartEngineTaskAssigneeDispatcher.class);


    private final InstanceAccessor instanceAccessor;


    public SmartEngineTaskAssigneeDispatcher(InstanceAccessor instanceAccessor) {
        this.instanceAccessor = instanceAccessor;
    }

    /**
     * 缓存已扫描的分配器实现
     */
    private WorkflowTaskAssigneeDispatcher dispatcher;

    /**
     * 是否已初始化
     */
    private volatile boolean initialized = false;

    @Override
    public List<TaskAssigneeCandidateInstance> getTaskAssigneeCandidateInstance(Activity activity, ExecutionContext context) {
        try {
            // 延迟初始化分配器
            if (!initialized) {
                synchronized (this) {
                    if (!initialized) {
                        dispatcher = initializeDispatchers();
                        initialized = true;
                    }
                }
            }

            // 构建输入参数
            Map<String, Object> inputParameter = buildInputParameter(activity, context);

            // 查找合适的分配器

            if (dispatcher == null) {
                String errorMsg = "No WorkflowTaskAssigneeDispatcher implementation found";
                LOGGER.error(errorMsg);
                throw new RuntimeException(errorMsg);
            }

            // 执行分配逻辑
            List<WorkflowTaskAssigneeCandidateInstance> result = dispatcher.dispatch(inputParameter);

            // 转换结果
            return convertToSmartEngineFormat(result);

        } catch (Exception e) {
            LOGGER.error("Failed to dispatch task assignee for activity: " + activity.getId(), e);
            throw new RuntimeException("Task assignee dispatch failed: " + e.getMessage(), e);
        }
    }

    /**
     * 初始化所有WorkflowTaskAssigneeDispatcher实现
     */
    private WorkflowTaskAssigneeDispatcher initializeDispatchers() {
        if (instanceAccessor == null) {
            throw new RuntimeException("ApplicationContext is null, cannot scan for WorkflowTaskAssigneeDispatcher implementations");
        }

        try {
            // 首先尝试通过instanceAccessor获取WorkflowTaskAssigneeDispatcher的实现
            Object dispatcher = instanceAccessor.access("workflowTaskAssigneeDispatcher");

            if (dispatcher == null) {
                dispatcher = instanceAccessor.access(WorkflowTaskAssigneeDispatcher.class.getName());
            }

            if (dispatcher != null && !(dispatcher instanceof WorkflowTaskAssigneeDispatcher)) {
                throw new RuntimeException(String.format("dispatcher %s is not a WorkflowTaskAssigneeDispatcher implementation", dispatcher.getClass().getName()));
            }

            if (dispatcher != null) {
                LOGGER.info("Found WorkflowTaskAssigneeDispatcher implementation via instanceAccessor: {}", dispatcher.getClass().getName());
                return (WorkflowTaskAssigneeDispatcher) dispatcher;
            }

        } catch (Exception e) {
            LOGGER.warn("Failed to get WorkflowTaskAssigneeDispatcher via instanceAccessor, will try reflection fallback", e);
        }

        // 如果通过instanceAccessor无法找到或者抛出异常，就通过接口反射做最后的一次尝试
        try {
            LOGGER.info("Attempting to find WorkflowTaskAssigneeDispatcher implementation via reflection");
            WorkflowTaskAssigneeDispatcher reflectionDispatcher = findDispatcherByReflection();
            if (reflectionDispatcher != null) {
                LOGGER.info("Found WorkflowTaskAssigneeDispatcher implementation via reflection: {}", reflectionDispatcher.getClass().getName());
                return reflectionDispatcher;
            }
        } catch (Exception e) {
            LOGGER.error("Failed to find WorkflowTaskAssigneeDispatcher implementation via reflection", e);
        }

        throw new RuntimeException("Failed to initialize WorkflowTaskAssigneeDispatcher implementations via both instanceAccessor and reflection");
    }

    /**
     * 通过反射查找WorkflowTaskAssigneeDispatcher实现
     */
    private WorkflowTaskAssigneeDispatcher findDispatcherByReflection() {
        try {
            // 获取当前类路径下的所有类
            List<Class<?>> classes = findClassesInPackage("com.winit.workflow.agent");

            for (Class<?> clazz : classes) {
                // 检查是否实现了WorkflowTaskAssigneeDispatcher接口
                if (WorkflowTaskAssigneeDispatcher.class.isAssignableFrom(clazz) &&
                    !clazz.isInterface() &&
                    !Modifier.isAbstract(clazz.getModifiers())) {

                    try {
                        // 尝试创建实例 (使用JDK 8兼容的方式)
                        Object instance = clazz.getDeclaredConstructor().newInstance();
                        LOGGER.info("Successfully created WorkflowTaskAssigneeDispatcher instance: {}", clazz.getName());
                        return (WorkflowTaskAssigneeDispatcher) instance;
                    } catch (Exception e) {
                        LOGGER.warn("Failed to instantiate WorkflowTaskAssigneeDispatcher: {}", clazz.getName(), e);
                        // 继续尝试其他实现类
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error during reflection-based dispatcher discovery", e);
        }

        return null;
    }

    /**
     * 在指定包下查找所有类
     */
    private List<Class<?>> findClassesInPackage(String packageName) {
        List<Class<?>> classes = new ArrayList<>();
        try {
            String path = packageName.replace('.', '/');
            ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
            URL resource = classLoader.getResource(path);

            if (resource != null) {
                File directory = new File(resource.getFile());
                if (directory.exists()) {
                    findClassesInDirectory(directory, packageName, classes);
                }
            }
        } catch (Exception e) {
            LOGGER.warn("Error scanning package: {}", packageName, e);
        }

        return classes;
    }

    /**
     * 在目录中递归查找类文件
     */
    private void findClassesInDirectory(File directory, String packageName, List<Class<?>> classes) {
        if (!directory.exists()) {
            return;
        }

        File[] files = directory.listFiles();
        if (files == null) {
            return;
        }

        for (File file : files) {
            if (file.isDirectory()) {
                // 递归查找子目录
                findClassesInDirectory(file, packageName + "." + file.getName(), classes);
            } else if (file.getName().endsWith(".class")) {
                // 加载类文件
                String className = packageName + '.' + file.getName().substring(0, file.getName().length() - 6);
                try {
                    Class<?> clazz = Class.forName(className);
                    classes.add(clazz);
                } catch (ClassNotFoundException e) {
                    LOGGER.debug("Could not load class: {}", className);
                }
            }
        }
    }

    /**
     * 构建输入参数对象
     */
    private Map<String, Object> buildInputParameter(Activity activity, ExecutionContext context) {
        // 创建一个包含活动和上下文信息的输入对象
        Map<String, Object> inputData = new HashMap<>();
        inputData.put("activity", activity);
        inputData.put("context", context);

        return inputData;
    }


    /**
     * 转换结果为SmartEngine格式
     */
    private List<TaskAssigneeCandidateInstance> convertToSmartEngineFormat(List<WorkflowTaskAssigneeCandidateInstance> result) {
        if (result == null) {
            return Collections.emptyList();
        }
        return result.stream().map(r -> {
            TaskAssigneeCandidateInstance smartEngineInstance = new TaskAssigneeCandidateInstance();
            smartEngineInstance.setAssigneeId(r.getAssigneeId());
            smartEngineInstance.setAssigneeType(r.getAssigneeType());
            smartEngineInstance.setPriority(r.getPriority());
            return smartEngineInstance;
        }).collect(Collectors.toList());
    }
}
