package com.winit.workflow.engine.core.expression;

import java.util.List;
import java.util.Map;

/**
 * 表达式服务接口，提供表达式解析功能
 * <AUTHOR>
 */
public interface ExpressionService {

    /**
     * 解析表达式
     *
     * @param value 可能包含表达式的字符串
     * @param context 表达式上下文
     * @return 解析后的值
     */
    Object parseExpression(String value, Map<String, Object> context);

    /**
     * 注册自定义函数
     *
     * @param name 函数名称
     * @param function 函数实现
     */
    void registerFunction(String name, Object function);

    /**
     * 获取底层表达式引擎
     */
    ExpressionEngine getExpressionEngine();

    /**
     * 获取所有可用的表达式函数信息
     *
     * @return 函数信息列表
     */
    List<FunctionInfo> getAllFunctions();
}