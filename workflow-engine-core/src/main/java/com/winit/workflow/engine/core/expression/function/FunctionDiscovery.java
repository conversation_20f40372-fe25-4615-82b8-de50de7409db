package com.winit.workflow.engine.core.expression.function;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.ServiceLoader;

/**
 * 函数自动发现机制
 * 使用Java SPI机制自动发现和加载所有实现了ExpressionFunction接口的类
 */
public class FunctionDiscovery {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(FunctionDiscovery.class);
    
    /**
     * 发现所有可用的表达式函数
     * 
     * @return 发现的函数列表
     */
    public static List<ExpressionFunction> discoverFunctions() {
        List<ExpressionFunction> functions = new ArrayList<>();
        
        try {
            LOGGER.info("Starting function discovery using SPI mechanism");
            
            // 使用SPI机制加载所有ExpressionFunction实现
            ServiceLoader<ExpressionFunction> serviceLoader = ServiceLoader.load(ExpressionFunction.class);
            
            int count = 0;
            for (ExpressionFunction function : serviceLoader) {
                try {
                    // 验证函数
                    validateFunction(function);
                    functions.add(function);
                    count++;
                    
                    LOGGER.debug("Discovered function: {} ({})", 
                        function.getFunctionName(), function.getClass().getName());
                        
                } catch (Exception e) {
                    LOGGER.error("Failed to load function: {}", function.getClass().getName(), e);
                }
            }
            
            LOGGER.info("Function discovery completed. Found {} functions", count);
            
        } catch (Exception e) {
            LOGGER.error("Error during function discovery", e);
        }
        
        return functions;
    }
    
    /**
     * 验证函数的有效性
     * 
     * @param function 要验证的函数
     * @throws IllegalArgumentException 如果函数无效
     */
    private static void validateFunction(ExpressionFunction function) {
        if (function == null) {
            throw new IllegalArgumentException("Function cannot be null");
        }
        
        String name = function.getFunctionName();
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Function name cannot be null or empty for " + 
                function.getClass().getName());
        }
        
        // 验证函数名称格式（只允许字母、数字和下划线）
        if (!name.matches("^[a-zA-Z_][a-zA-Z0-9_]*$")) {
            throw new IllegalArgumentException("Invalid function name format: " + name + 
                " (only letters, numbers and underscores allowed)");
        }
        
        // 验证参数数量
        int paramCount = function.getParameterCount();
        if (paramCount < -1) {
            throw new IllegalArgumentException("Invalid parameter count: " + paramCount + 
                " (must be >= -1)");
        }
        
        LOGGER.debug("Function validation passed: {}", name);
    }
    
    /**
     * 发现并注册所有函数到指定的注册器
     * 
     * @param registry 函数注册器
     * @return 成功注册的函数数量
     */
    public static int discoverAndRegister(FunctionRegistry registry) {
        if (registry == null) {
            throw new IllegalArgumentException("Function registry cannot be null");
        }
        
        List<ExpressionFunction> functions = discoverFunctions();
        registry.registerFunctions(functions);
        
        LOGGER.info("Registered {} functions to registry", functions.size());
        return functions.size();
    }
}
