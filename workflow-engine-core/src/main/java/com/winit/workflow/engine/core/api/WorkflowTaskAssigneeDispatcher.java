package com.winit.workflow.engine.core.api;

import com.winit.workflow.engine.core.api.model.WorkflowTaskAssigneeCandidateInstance;

import java.util.List;
import java.util.Map;

/**
 * 任务分配者调度器接口
 * 主要用于外部扩展。
 * 可选扩展。
 * 设计目的是用来处理任务的处理者。
 * 该扩展主要用于 database模式下的 userTask 场景。
 */
public interface WorkflowTaskAssigneeDispatcher {

    /**
     * 任务分配者调度器
     *
     * @param input 输入参数，包含activity和context等信息
     * @return 任务分配者
     */
    List<WorkflowTaskAssigneeCandidateInstance> dispatch(Map<String, Object> input);
}
