package com.winit.workflow.engine.core.enums;

/**
 * 任务类型枚举
 * 定义了工作流中支持的任务类型
 * 
 * <AUTHOR>
 */
public enum WorkflowTaskType {
    
    /**
     * 用户任务 - 需要人工处理的任务，如审批、确认等
     */
    USER_TASK("userTask", "用户任务"),
    
    /**
     * 接收任务 - 等待外部系统回调的任务，如API回调、消息接收等
     */
    RECEIVE_TASK("receiveTask", "接收任务");

    /**
     * 任务类型代码
     */
    private final String code;
    
    /**
     * 任务类型显示名称
     */
    private final String displayName;

    /**
     * 构造函数
     * 
     * @param code 任务类型代码
     * @param displayName 任务类型显示名称
     */
    WorkflowTaskType(String code, String displayName) {
        this.code = code;
        this.displayName = displayName;
    }

    /**
     * 获取任务类型代码
     * 
     * @return 任务类型代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取任务类型显示名称
     * 
     * @return 任务类型显示名称
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * 根据任务类型代码获取枚举值
     * 
     * @param code 任务类型代码
     * @return 对应的枚举值
     * @throws IllegalArgumentException 当代码无效时抛出异常
     */
    public static WorkflowTaskType fromCode(String code) {
        if (code == null) {
            throw new IllegalArgumentException("任务类型代码不能为null");
        }
        
        for (WorkflowTaskType workflowTaskType : values()) {
            if (workflowTaskType.code.equals(code)) {
                return workflowTaskType;
            }
        }
        throw new IllegalArgumentException("未知的任务类型代码: " + code);
    }

    /**
     * 检查任务类型代码是否有效
     * 
     * @param code 任务类型代码
     * @return 如果代码有效返回true，否则返回false
     */
    public static boolean isValid(String code) {
        if (code == null) {
            return false;
        }
        
        for (WorkflowTaskType workflowTaskType : values()) {
            if (workflowTaskType.code.equals(code)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否为用户任务
     * 
     * @return 如果是用户任务返回true，否则返回false
     */
    public boolean isUserTask() {
        return this == USER_TASK;
    }

    /**
     * 判断是否为接收任务
     * 
     * @return 如果是接收任务返回true，否则返回false
     */
    public boolean isReceiveTask() {
        return this == RECEIVE_TASK;
    }

    /**
     * 返回任务类型代码
     * 
     * @return 任务类型代码
     */
    @Override
    public String toString() {
        return this.code;
    }
}
