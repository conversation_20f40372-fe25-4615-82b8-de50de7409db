package com.winit.workflow.engine.core.provider.smartengine;

import com.alibaba.smart.framework.engine.model.instance.TaskInstance;
import com.winit.workflow.engine.core.api.WorkflowTaskInstance;
import com.winit.workflow.engine.core.enums.WorkflowTaskType;

/**
 * <AUTHOR>
 * 用户任务
 */
public class SmartEngineWorkflowUserTaskInstance implements WorkflowTaskInstance<TaskInstance> {

    private final TaskInstance taskInstance;

    private final String workflowTaskType;

    public SmartEngineWorkflowUserTaskInstance(TaskInstance taskInstance) {
        this.taskInstance = taskInstance;
        this.workflowTaskType = WorkflowTaskType.USER_TASK.getCode();
    }

    @Override
    public String getId() {
        return taskInstance.getInstanceId();
    }

    @Override
    public String getTaskType() {
        return workflowTaskType;
    }

    @Override
    public TaskInstance taskInstance() {
        return taskInstance;
    }
}
