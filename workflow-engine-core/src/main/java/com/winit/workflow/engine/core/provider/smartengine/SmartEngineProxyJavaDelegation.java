package com.winit.workflow.engine.core.provider.smartengine;

import com.alibaba.smart.framework.engine.context.ExecutionContext;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.winit.workflow.engine.core.executor.service.ServiceExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.Objects;

/**
 * Smart Engine工作流引擎的Java委托代理实现
 *
 * <p>该类继承自 {@link AbstractExpressionJavaDelegation}，自动获得表达式解析能力。
 * 提供了对业务执行器的包装，优化了对象转换过程，提高了性能和可维护性。
 *
 * <AUTHOR>
 */
public class SmartEngineProxyJavaDelegation extends AbstractExpressionJavaDelegation {
    private static final Logger LOGGER = LoggerFactory.getLogger(SmartEngineProxyJavaDelegation.class);
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    private final ServiceExecutor executor;

    /**
     * 创建一个新的SmartEngineProxyJavaDelegation实例
     *
     * @param executor 业务执行器，不能为null
     * @throws NullPointerException 如果executor为null
     */
    public SmartEngineProxyJavaDelegation(ServiceExecutor executor) {
        this.executor = Objects.requireNonNull(executor, "ServiceExecutor must not be null");
    }

    @Override
    protected void doExecute(ExecutionContext executionContext, Map<String, Object> properties) {
        try {
            // 获取处理后的请求数据（表达式已由基类处理）
            Map<String, Object> request = executionContext.getRequest();
            LOGGER.debug("Processing request with {} parameters for ServiceExecutor", request.size());

            // 转换请求对象并执行业务逻辑
            Object result = executeBusinessLogic(request);

            // 处理响应
            handleResponse(executionContext, result);

            LOGGER.debug("Successfully executed ServiceExecutor delegation");
        } catch (Exception e) {
            LOGGER.error("Failed to execute ServiceExecutor delegation", e);
            throw new RuntimeException("ServiceExecutor delegation execution failed: " + e.getMessage(), e);
        }
    }



    /**
     * 执行业务逻辑
     *
     * @param request 请求数据（表达式已处理）
     * @return 执行结果
     */
    private Object executeBusinessLogic(Map<String, Object> request) {
        try {
            // 直接使用ObjectMapper将Map转换为目标类型，避免中间JSON字符串转换
            Object inputObject = OBJECT_MAPPER.convertValue(request, executor.getInType());
            LOGGER.debug("Converted request to {}: {}", executor.getInType().getSimpleName(), inputObject);

            Object result = executor.execute(inputObject);
            LOGGER.debug("ServiceExecutor execution completed with result type: {}",
                result != null ? result.getClass().getSimpleName() : "null");

            return result;
        } catch (IllegalArgumentException e) {
            throw new RuntimeException("Failed to convert request to " + executor.getInType().getName(), e);
        } catch (Exception e) {
            throw new RuntimeException("Failed to execute ServiceExecutor business logic", e);
        }
    }

    /**
     * 处理执行结果并更新响应
     *
     * @param context 执行上下文
     * @param result  执行结果
     */
    private void handleResponse(ExecutionContext context, Object result) {
        try {
            if (result != null) {
                // 直接使用ObjectMapper将结果对象转换为Map，避免中间JSON字符串转换
                @SuppressWarnings("unchecked")
                Map<String, Object> responseMap = OBJECT_MAPPER.convertValue(result, Map.class);

                // 清空响应并添加新的结果数据
                context.getResponse().clear();
                context.getResponse().putAll(responseMap);

                LOGGER.debug("Response updated with {} fields", responseMap.size());
            } else {
                LOGGER.debug("ServiceExecutor returned null result, response not updated");
            }
        } catch (IllegalArgumentException e) {
            throw new RuntimeException("Failed to convert ServiceExecutor response to Map", e);
        }
    }

    /**
     * 获取ServiceExecutor实例
     *
     * @return ServiceExecutor实例
     */
    public ServiceExecutor getExecutor() {
        return executor;
    }
}
