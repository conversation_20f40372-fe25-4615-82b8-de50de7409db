package com.winit.workflow.engine.core.expression.function;

import java.util.List;
import java.util.Map;

/**
 * 函数注册器接口，负责管理表达式函数的注册和发现
 */
public interface FunctionRegistry {
    
    /**
     * 注册单个函数
     * 
     * @param function 要注册的函数
     */
    void registerFunction(ExpressionFunction function);
    
    /**
     * 批量注册函数
     * 
     * @param functions 要注册的函数列表
     */
    void registerFunctions(List<ExpressionFunction> functions);
    
    /**
     * 获取已注册的函数
     * 
     * @param name 函数名称
     * @return 函数实例，如果不存在返回null
     */
    ExpressionFunction getFunction(String name);
    
    /**
     * 获取所有已注册的函数
     * 
     * @return 函数名称到函数实例的映射
     */
    Map<String, ExpressionFunction> getAllFunctions();
    
    /**
     * 检查函数是否已注册
     * 
     * @param name 函数名称
     * @return 如果已注册返回true，否则返回false
     */
    boolean hasFunction(String name);
    
    /**
     * 取消注册函数
     * 
     * @param name 函数名称
     * @return 如果成功取消注册返回true，否则返回false
     */
    boolean unregisterFunction(String name);
    
    /**
     * 清空所有已注册的函数
     */
    void clear();
    
    /**
     * 获取已注册函数的数量
     * 
     * @return 函数数量
     */
    int size();
}
