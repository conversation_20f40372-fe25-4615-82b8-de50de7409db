package com.winit.workflow.engine.core.api;

import com.alibaba.smart.framework.engine.model.instance.ActivityInstance;
import com.alibaba.smart.framework.engine.model.instance.InstanceStatus;

import java.util.List;

/**
 * 工作流实例接口
 * 定义了工作流实例的基本操作
 */
public interface WorkflowInstance {

    /**
     * get ProcessDefinitionId and ProcessDefinitionVersion
     * @return
     */
    String getProcessDefinitionIdAndVersion();
    String getUniqueProcessDefinitionIdAndVersion();

    /**
     * get ProcessDefinitionId
     * @return
     */
    String getProcessDefinitionId();

    /**
     * get ProcessDefinitionVersion
     * @return
     */
    String getProcessDefinitionVersion();

    String getProcessDefinitionType();

    String getStartUserId();

    String getParentInstanceId();

    String getParentExecutionInstanceId();

    InstanceStatus getStatus();

    boolean isSuspend();

    List<ActivityInstance> getActivityInstances();

    String getBizUniqueId();


    String getReason();


    String getTag();


    String getTitle();


    String getComment();

    /**
     * 获取实例ID
     *
     * @return 实例ID
     */

    String getInstanceId();

} 