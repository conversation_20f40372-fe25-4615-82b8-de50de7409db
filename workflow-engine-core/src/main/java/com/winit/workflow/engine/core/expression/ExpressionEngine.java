package com.winit.workflow.engine.core.expression;

import java.util.Map;

/**
 * 表达式引擎接口，用于解析和执行表达式
 * <AUTHOR>
 */
public interface ExpressionEngine {
    
    /**
     * 解析并执行表达式
     * 
     * @param expression 要执行的表达式
     * @param context 表达式执行上下文
     * @return 表达式执行结果
     */
    Object evaluate(String expression, Map<String, Object> context);
    
    /**
     * 注册自定义函数
     * 
     * @param name 函数名称
     * @param function 函数实现
     */
    void registerFunction(String name, Object function);
    
    /**
     * 判断字符串是否为表达式
     * 
     * @param value 要检查的字符串
     * @return 如果是表达式返回true，否则返回false
     */
    boolean isExpression(String value);
}