package com.winit.workflow.engine.core.provider.smartengine;

import com.alibaba.smart.framework.engine.constant.ExtensionElementsConstant;
import com.alibaba.smart.framework.engine.context.ExecutionContext;
import com.alibaba.smart.framework.engine.delegation.JavaDelegation;
import com.alibaba.smart.framework.engine.model.assembly.BaseElement;
import com.alibaba.smart.framework.engine.model.assembly.impl.AbstractActivity;
import com.alibaba.smart.framework.engine.model.assembly.impl.AbstractTask;
import com.alibaba.smart.framework.engine.smart.PropertyCompositeKey;
import com.alibaba.smart.framework.engine.smart.PropertyCompositeValue;
import com.winit.workflow.engine.core.expression.ExpressionService;
import com.winit.workflow.engine.core.expression.ExpressionServiceFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 抽象表达式Java委托基类
 *
 * <p>该抽象类提供了表达式解析的通用功能，包括：
 * <ul>
 *   <li>递归表达式解析（支持String、Map、List类型）</li>
 *   <li>扩展元素属性提取</li>
 *   <li>执行上下文的通用处理逻辑</li>
 *   <li>统一的日志记录</li>
 * </ul>
 *
 * <p>子类只需要实现 {@link #doExecute(ExecutionContext, Map)} 方法来专注于具体的业务逻辑，
 * 表达式解析和属性提取等通用功能由基类自动处理。
 *
 * <AUTHOR>
 */
public abstract class AbstractExpressionJavaDelegation implements JavaDelegation {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractExpressionJavaDelegation.class);

    /**
     * 表达式服务实例，用于解析表达式
     */
    protected final ExpressionService expressionService = ExpressionServiceFactory.getInstance();

    @Override
    public final void execute(ExecutionContext executionContext) {
        try {
            // 验证执行上下文
            validateExecutionContext(executionContext);

            // 提取扩展元素属性
            Map<String, Object> properties = extractProperties(executionContext);

            // 记录执行信息
            logExecutionInfo(executionContext);

            // 初始化响应数据
            initializeResponse(executionContext, properties);

            // 处理请求中的表达式
            Map<String, Object> processedRequest = processRequestExpressions(executionContext);

            // 处理属性中的表达式
            Map<String, Object> processedProperties = processPropertiesExpressions(properties, processedRequest);

            // 更新执行上下文的请求数据（包含处理后的属性）
            updateExecutionContextRequest(executionContext, processedRequest, processedProperties);

            // 执行具体的业务逻辑
            doExecute(executionContext, processedProperties);

            // 处理响应中的表达式
            processResponseExpressions(executionContext);

        } catch (Exception e) {
            LOGGER.error("Failed to execute delegation for activity: {}",
                getActivityId(executionContext), e);
            throw new RuntimeException("Delegation execution failed: " + e.getMessage(), e);
        }
    }

    /**
     * 子类需要实现的具体业务逻辑
     *
     * @param executionContext 执行上下文
     * @param properties 从扩展元素中提取的属性
     */
    protected abstract void doExecute(ExecutionContext executionContext, Map<String, Object> properties);

    /**
     * 验证执行上下文
     *
     * @param executionContext 执行上下文
     * @throws IllegalArgumentException 如果上下文无效
     */
    protected void validateExecutionContext(ExecutionContext executionContext) {
        if (executionContext == null) {
            throw new IllegalArgumentException("ExecutionContext cannot be null");
        }
        if (executionContext.getRequest() == null) {
            throw new IllegalArgumentException("ExecutionContext request cannot be null");
        }
        if (executionContext.getResponse() == null) {
            throw new IllegalArgumentException("ExecutionContext response cannot be null");
        }
    }

    /**
     * 从扩展元素中提取属性
     *
     * @param executionContext 执行上下文
     * @return 提取的属性Map
     */
    protected Map<String, Object> extractProperties(ExecutionContext executionContext) {
        Map<String, Object> properties = new HashMap<>();
        BaseElement baseElement = executionContext.getBaseElement();

        if (baseElement instanceof AbstractActivity) {
            AbstractTask abstractTask = (AbstractTask) baseElement;
            Map<String, Object> decorationMap = abstractTask.getExtensionElements().getDecorationMap();

            @SuppressWarnings("unchecked")
            Map<PropertyCompositeKey, PropertyCompositeValue> propertyCompositeKeyMap =
                (Map<PropertyCompositeKey, PropertyCompositeValue>) decorationMap.get(ExtensionElementsConstant.PROPERTIES);

            if (propertyCompositeKeyMap != null) {
                propertyCompositeKeyMap.forEach((k, v) -> properties.put(k.getName(), v.getValue()));
            }
        }

        return properties;
    }

    /**
     * 记录执行信息
     *
     * @param executionContext 执行上下文
     */
    protected void logExecutionInfo(ExecutionContext executionContext) {
        String activityId = getActivityId(executionContext);
        LOGGER.info("Executing service task for activity: {}", activityId);
        LOGGER.debug("Request parameters count: {}", executionContext.getRequest().size());
    }

    /**
     * 初始化响应数据
     *
     * @param executionContext 执行上下文
     * @param properties 扩展属性（原始未处理的）
     */
    protected void initializeResponse(ExecutionContext executionContext, Map<String, Object> properties) {
        // 将请求数据复制到响应中
        executionContext.getResponse().putAll(executionContext.getRequest());

        // 将原始属性添加到请求中（稍后会被表达式处理替换）
        if (!properties.isEmpty()) {
            executionContext.getRequest().putAll(properties);
            executionContext.getResponse().putAll(properties);
        }
    }

    /**
     * 处理请求中的表达式
     *
     * @param executionContext 执行上下文
     * @return 处理后的请求数据
     */
    protected Map<String, Object> processRequestExpressions(ExecutionContext executionContext) {
        Map<String, Object> processedRequest = new HashMap<>();
        Map<String, Object> originalRequest = executionContext.getRequest();

        for (Map.Entry<String, Object> entry : originalRequest.entrySet()) {
            processedRequest.put(entry.getKey(), processValue(entry.getValue(), originalRequest));
        }

        return processedRequest;
    }

    /**
     * 处理属性中的表达式
     *
     * @param properties 原始属性
     * @param context 表达式上下文（已处理的请求数据）
     * @return 处理后的属性
     */
    protected Map<String, Object> processPropertiesExpressions(Map<String, Object> properties, Map<String, Object> context) {
        Map<String, Object> processedProperties = new HashMap<>();

        for (Map.Entry<String, Object> entry : properties.entrySet()) {
            processedProperties.put(entry.getKey(), processValue(entry.getValue(), context));
        }

        return processedProperties;
    }

    /**
     * 更新执行上下文的请求数据
     *
     * @param executionContext 执行上下文
     * @param processedRequest 处理后的请求数据
     * @param processedProperties 处理后的属性数据
     */
    protected void updateExecutionContextRequest(ExecutionContext executionContext,
                                                Map<String, Object> processedRequest,
                                                Map<String, Object> processedProperties) {
        // 清空并更新请求数据
        executionContext.getRequest().clear();
        executionContext.getRequest().putAll(processedRequest);
        executionContext.getRequest().putAll(processedProperties);

        // 同时更新响应数据，确保响应也包含处理后的属性
        executionContext.getResponse().clear();
        executionContext.getResponse().putAll(processedRequest);
        executionContext.getResponse().putAll(processedProperties);
    }

    /**
     * 处理响应中的表达式
     *
     * @param executionContext 执行上下文
     */
    protected void processResponseExpressions(ExecutionContext executionContext) {
        Map<String, Object> processedResponse = new HashMap<>();
        Map<String, Object> originalResponse = executionContext.getResponse();
        Map<String, Object> requestContext = executionContext.getRequest();

        for (Map.Entry<String, Object> entry : originalResponse.entrySet()) {
            processedResponse.put(entry.getKey(), processValue(entry.getValue(), requestContext));
        }

        // 更新响应数据
        executionContext.getResponse().clear();
        executionContext.getResponse().putAll(processedResponse);
    }

    /**
     * 递归处理值中的表达式
     *
     * @param value 要处理的值
     * @param context 表达式上下文
     * @return 处理后的值
     */
    protected Object processValue(Object value, Map<String, Object> context) {
        if (value instanceof String) {
            return expressionService.parseExpression((String) value, context);
        } else if (value instanceof Map) {
            return processMapValue((Map<?, ?>) value, context);
        } else if (value instanceof List) {
            return processListValue((List<?>) value, context);
        }
        return value;
    }

    /**
     * 处理Map类型的值
     *
     * @param originalMap 原始Map
     * @param context 表达式上下文
     * @return 处理后的Map
     */
    @SuppressWarnings("unchecked")
    protected Map<Object, Object> processMapValue(Map<?, ?> originalMap, Map<String, Object> context) {
        Map<Object, Object> processedMap = new HashMap<>();

        for (Map.Entry<?, ?> entry : originalMap.entrySet()) {
            Object processedKey = entry.getKey() instanceof String
                ? processValue(entry.getKey(), context) : entry.getKey();
            Object processedValue = processValue(entry.getValue(), context);
            processedMap.put(processedKey, processedValue);
        }

        return processedMap;
    }

    /**
     * 处理List类型的值
     *
     * @param originalList 原始List
     * @param context 表达式上下文
     * @return 处理后的List
     */
    protected List<Object> processListValue(List<?> originalList, Map<String, Object> context) {
        List<Object> processedList = new ArrayList<>(originalList.size());

        for (Object item : originalList) {
            processedList.add(processValue(item, context));
        }

        return processedList;
    }

    /**
     * 获取活动ID
     *
     * @param executionContext 执行上下文
     * @return 活动ID
     */
    protected String getActivityId(ExecutionContext executionContext) {
        return executionContext.getExecutionInstance().getProcessDefinitionActivityId();
    }

    /**
     * 获取表达式服务实例
     *
     * @return 表达式服务实例
     */
    protected ExpressionService getExpressionService() {
        return expressionService;
    }
}
