package com.winit.workflow.engine.core.expression.function.builtin;

import com.winit.workflow.engine.core.expression.function.ExpressionFunction;

/**
 * 字符串转小写函数
 */
public class LowercaseFunction implements ExpressionFunction {
    
    @Override
    public String getFunctionName() {
        return "lowercase";
    }
    
    @Override
    public String getDescription() {
        return "Converts a string to lowercase";
    }
    
    @Override
    public int getParameterCount() {
        return 1;
    }
    
    @Override
    public int getPriority() {
        return 10; // 内置函数使用较高优先级
    }
    
    @Override
    public Object execute(Object... args) {
        if (args == null || args.length == 0) {
            return "";
        }
        
        Object arg = args[0];
        return arg != null ? arg.toString().toLowerCase() : "";
    }
    
    @Override
    public boolean validateParameters(Object... args) {
        return args != null && args.length == 1;
    }

    @Override
    public String getExample() {
        return "${lowercase('HELLO WORLD')}\n// Result: \"hello world\"\n\n${lowercase(companyName)}\n// Converts variable companyName to lowercase";
    }

    @Override
    public String getFunctionSignature() {
        return "lowercase(text): converts a string to lowercase\nParameters: text - the string to convert";
    }
}
