package com.winit.workflow.engine.core.provider.smartengine;

import com.alibaba.smart.framework.engine.context.ExecutionContext;
import com.alibaba.smart.framework.engine.listener.Listener;
import com.alibaba.smart.framework.engine.pvm.event.EventConstant;
import com.winit.workflow.engine.core.listener.ListenerService;


/**
 * SmartEngine监听器实现
 *
 * <AUTHOR>
 */
public class SmartEngineProxyListener implements Listener {

    private ListenerService listenerService;

    public SmartEngineProxyListener(ListenerService listenerService) {
        if (listenerService == null) {
            throw new NullPointerException("ListenerService must not be null");
        }
        this.listenerService = listenerService;
    }

    @Override
    public void execute(EventConstant event, ExecutionContext executionContext) {
        listenerService.execute(event.name(), executionContext.getRequest(), executionContext.getResponse());
    }

}
