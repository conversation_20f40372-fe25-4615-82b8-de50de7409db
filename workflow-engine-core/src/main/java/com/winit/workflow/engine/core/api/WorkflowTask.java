package com.winit.workflow.engine.core.api;

/**
 * 工作流任务接口
 * 定义了工作流任务的基本操作
 */
public interface WorkflowTask {
    
    /**
     * 获取任务ID
     * @return 任务ID
     */
    String getId();
    
    /**
     * 获取任务名称
     * @return 任务名称
     */
    String getName();
    
    /**
     * 获取处理人
     * @return 处理人
     */
    String getAssignee();
    
    /**
     * 设置处理人
     * @param assignee 处理人
     */
    void setAssignee(String assignee);
    
    /**
     * 获取任务变量
     * @return 任务变量
     */
    Object getVariables();
    
    /**
     * 完成任务
     * @param variables 完成变量
     */
    void complete(Object variables);
    
    /**
     * 认领任务
     * @param assignee 处理人
     */
    void claim(String assignee);
    
    /**
     * 取消认领
     */
    void unclaim();
} 