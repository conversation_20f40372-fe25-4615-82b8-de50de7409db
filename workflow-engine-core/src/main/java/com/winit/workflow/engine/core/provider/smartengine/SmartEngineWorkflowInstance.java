package com.winit.workflow.engine.core.provider.smartengine;

import com.alibaba.smart.framework.engine.model.instance.ActivityInstance;
import com.alibaba.smart.framework.engine.model.instance.InstanceStatus;
import com.alibaba.smart.framework.engine.model.instance.ProcessInstance;
import com.winit.workflow.engine.core.api.WorkflowInstance;

import java.util.List;

/**
 * <AUTHOR>
 * 工作流实例适配器
 */
public class SmartEngineWorkflowInstance implements WorkflowInstance {

    private final ProcessInstance processInstance;

    public SmartEngineWorkflowInstance(ProcessInstance processInstance) {
        this.processInstance = processInstance;
    }

    @Override
    public String getProcessDefinitionIdAndVersion() {
        return processInstance.getProcessDefinitionIdAndVersion();
    }

    @Override
    public String getUniqueProcessDefinitionIdAndVersion() {
        return processInstance.getUniqueProcessDefinitionIdAndVersion();
    }

    @Override
    public String getProcessDefinitionId() {
        return processInstance.getProcessDefinitionId();
    }

    @Override
    public String getProcessDefinitionVersion() {
        return processInstance.getProcessDefinitionVersion();
    }

    @Override
    public String getProcessDefinitionType() {
        return processInstance.getProcessDefinitionType();
    }

    @Override
    public String getStartUserId() {
        return processInstance.getStartUserId();
    }

    @Override
    public String getParentInstanceId() {
        return processInstance.getParentInstanceId();
    }

    @Override
    public String getParentExecutionInstanceId() {
        return processInstance.getParentExecutionInstanceId();
    }

    @Override
    public InstanceStatus getStatus() {
        return processInstance.getStatus();
    }

    @Override
    public boolean isSuspend() {
        return processInstance.isSuspend();
    }

    @Override
    public List<ActivityInstance> getActivityInstances() {
        return processInstance.getActivityInstances();
    }

    @Override
    public String getBizUniqueId() {
        return processInstance.getBizUniqueId();
    }

    @Override
    public String getReason() {
        return processInstance.getReason();
    }

    @Override
    public String getTag() {
        return processInstance.getTag();
    }

    @Override
    public String getTitle() {
        return processInstance.getTitle();
    }

    @Override
    public String getComment() {
        return processInstance.getComment();
    }

    @Override
    public String getInstanceId() {
        return processInstance.getInstanceId();
    }
}
