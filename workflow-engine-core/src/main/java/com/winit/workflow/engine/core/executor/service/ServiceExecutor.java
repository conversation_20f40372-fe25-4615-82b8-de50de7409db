package com.winit.workflow.engine.core.executor.service;


import com.winit.workflow.engine.core.executor.Executor;

import java.lang.reflect.ParameterizedType;

public abstract class ServiceExecutor<IN extends ServiceExecutorRequest<IN>, OUT extends ServiceExecutorResponse<OUT>>
        implements Executor<IN, OUT> {

    @SuppressWarnings("unchecked")
    public Class<IN> getInType() {
        return (Class<IN>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }

    @SuppressWarnings("unchecked")
    public Class<OUT> getOutType() {
        return (Class<OUT>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[1];
    }
}
