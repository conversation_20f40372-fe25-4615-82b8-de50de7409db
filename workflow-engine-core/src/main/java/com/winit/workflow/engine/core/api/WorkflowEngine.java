package com.winit.workflow.engine.core.api;



import java.util.List;
import java.util.Map;

/**
 * 工作流引擎接口
 * 定义了工作流引擎的基本操作
 *
 * <AUTHOR>
 */
public interface WorkflowEngine {

    /**
     * 部署工作流定义
     * @param deploymentUserId
     * @param processDefinitionName
     * @param processDefinitionType
     * @param processDefinitionCode
     * @param processDefinitionId
     * @param processDefinitionVersion
     * @param processDefinitionDesc
     * @param processDefinitionContent
     * @return
     */
    WorkflowDeploymentInstance deployProcess(String deploymentUserId,
                                             String processDefinitionName,
                                             String processDefinitionType,
                                             String processDefinitionCode,
                                             String processDefinitionId,
                                             String processDefinitionVersion,
                                             String processDefinitionDesc,
                                             String processDefinitionContent,
                                             String tenantId);

    /**
     * 卸载已部署工作定义
     *
     * @param processDefinitionId
     * @param processDefinitionType
     * @param processDefinitionCode
     * @param tenantId
     */
    void uninstallProcess(String processDefinitionId,
                          String processDefinitionType,
                          String processDefinitionCode,
                          String tenantId);

    /**
     * 启动工作流实例
     *
     * @param processDefinitionId 工作流定义ID
     * @param variables           启动变量
     * @Param response           响应参数
     * @param tenantId            租户ID
     * @return 工作流实例ID
     */
    WorkflowInstance startProcess(String processDefinitionId,
                                  String processDefinitionVersion,
                                  Map<String, Object> variables,
                                  Map<String, Object> response,
                                  String tenantId);

    /**
     * 完成任务
     *
     * @param taskInstanceId    任务ID
     * @param taskInstanceType    任务类型
     * @Param tenantId          租户ID
     * @param variables 完成变量
     */
    WorkflowInstance completeTask(String taskInstanceId, String taskInstanceType, Map<String, Object> variables, String tenantId);

    /**
     * 获取工作流实例
     *
     * @param processInstanceId 工作流实例ID
     * @Param tenantId          租户ID
     * @return 工作流实例
     */
    WorkflowInstance getProcessInstance(String processInstanceId, String tenantId);

    /**
     * 获取任务列表
     *
     * @param processInstanceId 工作流实例ID
     * @param variables         查询参数
     * @param tenantId          租户ID
     * @return processInstanceId
     */
    List<WorkflowTaskInstance<?>> getTasks(String processInstanceId, Map<String, Object> variables, String tenantId);


    /**
     * 加载已部署的流程定义
     * 加载到内存里
     */
    void loadDeployedFlows();

}