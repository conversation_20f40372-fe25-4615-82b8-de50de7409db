package com.winit.workflow.engine.core.api;

/**
 * 部署流程的实例
 *
 * <AUTHOR>
 */
public interface WorkflowDeploymentInstance {

    /**
     * 获取流程定义ID
     */
    String getProcessDefinitionId();

    /**
     * 获取流程定义版本
     */
    String getProcessDefinitionVersion();

    /**
     * 获取流程定义类型
     */
    String getProcessDefinitionType();

    /**
     * 获取流程定义编码
     */
    String getProcessDefinitionCode();

    /**
     * 获取流程定义名称
     */
    String getProcessDefinitionName();

    /**
     * 获取流程定义描述
     */
    String getProcessDefinitionDesc();

    /**
     * 获取部署用户ID
     */
    String getDeploymentUserId();

    /**
     * 获取流程定义内容
     */
    String getProcessDefinitionContent();

    /**
     * 获取部署状态
     */
    String getDeploymentStatus();

    /**
     * 获取逻辑状态
     */
    String getLogicStatus();

    /**
     * 获取实例ID
     * @return
     */
    String getInstanceId();

}