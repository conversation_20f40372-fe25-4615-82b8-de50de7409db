package com.winit.workflow.engine.core.provider.smartengine;

import com.alibaba.smart.framework.engine.model.instance.ExecutionInstance;
import com.winit.workflow.engine.core.api.WorkflowTaskInstance;
import com.winit.workflow.engine.core.enums.WorkflowTaskType;

/**
 * <AUTHOR>
 * 事件任务
 */
public class SmartEngineWorkflowReceiveTaskInstance implements WorkflowTaskInstance<ExecutionInstance> {

    private final ExecutionInstance taskInstance;

    private final String workflowTaskType;

    public SmartEngineWorkflowReceiveTaskInstance(ExecutionInstance taskInstance) {
        this.taskInstance = taskInstance;
        this.workflowTaskType = WorkflowTaskType.RECEIVE_TASK.getCode();
    }

    @Override
    public String getId() {
        return taskInstance.getInstanceId();
    }

    @Override
    public String getTaskType() {
        return workflowTaskType;
    }

    @Override
    public ExecutionInstance taskInstance() {
        return taskInstance;
    }
}
