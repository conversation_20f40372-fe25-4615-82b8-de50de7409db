package com.winit.workflow.engine.core.expression.function.builtin;

import com.winit.workflow.engine.core.expression.function.ExpressionFunction;

import java.util.Date;

/**
 * 获取当前时间函数
 */
public class NowFunction implements ExpressionFunction {
    
    @Override
    public String getFunctionName() {
        return "now";
    }
    
    @Override
    public String getDescription() {
        return "Returns the current date and time";
    }
    
    @Override
    public int getParameterCount() {
        return 0;
    }
    
    @Override
    public int getPriority() {
        return 10; // 内置函数使用较高优先级
    }
    
    @Override
    public Object execute(Object... args) {
        return new Date();
    }
    
    @Override
    public boolean validateParameters(Object... args) {
        return args == null || args.length == 0;
    }

    @Override
    public String getExample() {
        return "${now()}\n// Result: current date and time object\n// Example: Mon Jun 20 14:30:25 CST 2025";
    }

    @Override
    public String getFunctionSignature() {
        return "now(): returns the current date and time\nReturns: Date object representing the current date and time";
    }
}
