package com.winit.workflow.engine.core.expression.function.custom;

import com.winit.workflow.engine.core.expression.function.ExpressionFunction;

/**
 * Maximum value function
 * Finds the maximum value from multiple arguments
 */
public class MaxFunction implements ExpressionFunction {
    
    @Override
    public String getFunctionName() {
        return "max";
    }
    
    @Override
    public String getDescription() {
        return "Returns the maximum value from multiple arguments";
    }
    
    @Override
    public int getParameterCount() {
        return -1; // Variable parameters
    }

    @Override
    public String getParameterInfo() {
        return "2+"; // 2 or more parameters
    }
    
    @Override
    public int getPriority() {
        return 200; // Custom function priority
    }
    
    @Override
    public Object execute(Object... args) {
        if (args == null || args.length == 0) {
            throw new IllegalArgumentException("max function requires at least one argument");
        }
        
        Object maxValue = null;
        double maxNumeric = Double.NEGATIVE_INFINITY;
        boolean hasNumeric = false;
        
        for (Object arg : args) {
            if (arg == null) {
                continue;
            }
            
            try {
                double numericValue = parseNumeric(arg);
                if (!hasNumeric || numericValue > maxNumeric) {
                    maxNumeric = numericValue;
                    maxValue = arg;
                    hasNumeric = true;
                }
            } catch (NumberFormatException e) {
                // If we can't parse as number, compare as strings
                if (!hasNumeric) {
                    if (maxValue == null || arg.toString().compareTo(maxValue.toString()) > 0) {
                        maxValue = arg;
                    }
                }
            }
        }
        
        return maxValue;
    }
    
    private double parseNumeric(Object obj) throws NumberFormatException {
        if (obj instanceof Number) {
            return ((Number) obj).doubleValue();
        } else {
            return Double.parseDouble(obj.toString());
        }
    }
    
    @Override
    public boolean validateParameters(Object... args) {
        return args != null && args.length > 0;
    }
    
    @Override
    public String getExample() {
        return "${max(1, 5, 3, 9, 2)}\n// Result: 9\n\n${max(3.14, 2.71, 1.41)}\n// Result: 3.14\n\n${max(value1, value2, value3)}\n// Returns maximum of variables";
    }
    
    @Override
    public String getFunctionSignature() {
        return "max(value1, value2, ...): returns the maximum value from arguments\nParameters: value1, value2, ... - values to compare (variable arguments)";
    }
}
