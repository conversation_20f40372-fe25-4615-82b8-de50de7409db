package com.winit.workflow.engine.core.provider.smartengine;

import com.alibaba.smart.framework.engine.configuration.InstanceAccessor;
import com.winit.workflow.engine.core.api.InstanceResolver;
import com.winit.workflow.engine.core.executor.service.ServiceExecutor;
import com.winit.workflow.engine.core.listener.ListenerService;

/**
 * 对象实例
 *
 * <AUTHOR>
 */
public class SmartEngineInstanceAccessor implements InstanceAccessor {

    public SmartEngineInstanceAccessor(InstanceResolver instanceResolver) {
        if (instanceResolver == null) {
            throw new IllegalArgumentException("InstanceResolver must not be null");
        }
        this.instanceResolver = instanceResolver;
    }

    private final InstanceResolver instanceResolver;

    @Override
    public Object access(String classNameOrBeanName) {
        Object object = instanceResolver.resolve(classNameOrBeanName);
        if (object instanceof ServiceExecutor) {
            return new SmartEngineProxyJavaDelegation((ServiceExecutor) object);
        } else if (object instanceof ListenerService) {
            return new SmartEngineProxyListener((ListenerService) object);
        } else {
            return object;
        }

    }
}
