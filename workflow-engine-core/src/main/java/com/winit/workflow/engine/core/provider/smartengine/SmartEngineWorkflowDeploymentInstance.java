package com.winit.workflow.engine.core.provider.smartengine;

import com.alibaba.smart.framework.engine.model.instance.DeploymentInstance;
import com.winit.workflow.engine.core.api.WorkflowDeploymentInstance;

public class SmartEngineWorkflowDeploymentInstance implements WorkflowDeploymentInstance {

    private DeploymentInstance deploymentInstance;

    public SmartEngineWorkflowDeploymentInstance(DeploymentInstance deploymentInstance) {
        this.deploymentInstance = deploymentInstance;
    }

    @Override
    public String getProcessDefinitionId() {
        return deploymentInstance.getProcessDefinitionId();
    }

    @Override
    public String getProcessDefinitionVersion() {
        return deploymentInstance.getProcessDefinitionVersion();
    }

    @Override
    public String getProcessDefinitionType() {
        return deploymentInstance.getProcessDefinitionType();
    }

    @Override
    public String getProcessDefinitionCode() {
        return deploymentInstance.getProcessDefinitionCode();
    }

    @Override
    public String getProcessDefinitionName() {
        return deploymentInstance.getProcessDefinitionName();
    }

    @Override
    public String getProcessDefinitionDesc() {
        return deploymentInstance.getProcessDefinitionDesc();
    }

    @Override
    public String getDeploymentUserId() {
        return deploymentInstance.getDeploymentUserId();
    }

    @Override
    public String getProcessDefinitionContent() {
        return deploymentInstance.getProcessDefinitionContent();
    }

    @Override
    public String getDeploymentStatus() {
        return deploymentInstance.getDeploymentStatus();
    }

    @Override
    public String getLogicStatus() {
        return deploymentInstance.getLogicStatus();
    }

    @Override
    public String getInstanceId() {
        return deploymentInstance.getInstanceId();
    }
}
