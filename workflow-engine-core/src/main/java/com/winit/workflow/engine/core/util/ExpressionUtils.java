package com.winit.workflow.engine.core.util;

import com.winit.workflow.engine.core.expression.ExpressionService;
import com.winit.workflow.engine.core.expression.ExpressionServiceFactory;

import java.util.Map;

/**
 * 表达式工具类，提供便捷的表达式解析方法
 */
public class ExpressionUtils {

    private static final ExpressionService expressionService = ExpressionServiceFactory.getInstance();

    /**
     * 解析表达式
     *
     * @param expression 表达式字符串
     * @param context 表达式上下文
     * @return 解析结果
     */
    public static Object parseExpression(String expression, Map<String, Object> context) {
        return expressionService.parseExpression(expression, context);
    }

    /**
     * 解析表达式并转换为字符串
     *
     * @param expression 表达式字符串
     * @param context 表达式上下文
     * @return 解析结果的字符串表示
     */
    public static String parseExpressionAsString(String expression, Map<String, Object> context) {
        Object result = parseExpression(expression, context);
        return result != null ? result.toString() : "";
    }

    /**
     * 测试表达式是否为真
     *
     * @param expression 表达式字符串
     * @param context 表达式上下文
     * @return 如果表达式结果为true则返回true，否则返回false
     */
    public static boolean evaluateCondition(String expression, Map<String, Object> context) {
        Object result = parseExpression(expression, context);
        if (result instanceof Boolean) {
            return (Boolean) result;
        }
        return false;
    }
}