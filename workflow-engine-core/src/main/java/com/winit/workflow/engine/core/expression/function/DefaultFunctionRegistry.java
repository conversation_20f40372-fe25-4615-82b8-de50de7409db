package com.winit.workflow.engine.core.expression.function;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 默认函数注册器实现
 */
public class DefaultFunctionRegistry implements FunctionRegistry {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultFunctionRegistry.class);
    
    // 使用ConcurrentHashMap保证线程安全
    private final Map<String, ExpressionFunction> functions = new ConcurrentHashMap<>();
    
    @Override
    public void registerFunction(ExpressionFunction function) {
        if (function == null) {
            throw new IllegalArgumentException("Function cannot be null");
        }
        
        String name = function.getFunctionName();
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Function name cannot be null or empty");
        }
        
        // 检查是否已存在同名函数
        ExpressionFunction existing = functions.get(name);
        if (existing != null) {
            // 比较优先级，优先级低的（数值小的）覆盖优先级高的
            if (function.getPriority() <= existing.getPriority()) {
                functions.put(name, function);
                LOGGER.info("Function '{}' registered successfully (replaced existing with priority {} -> {})", 
                    name, existing.getPriority(), function.getPriority());
            } else {
                LOGGER.warn("Function '{}' registration skipped due to lower priority ({} vs {})", 
                    name, function.getPriority(), existing.getPriority());
                return;
            }
        } else {
            functions.put(name, function);
            LOGGER.info("Function '{}' registered successfully with priority {}", name, function.getPriority());
        }
    }
    
    @Override
    public void registerFunctions(List<ExpressionFunction> functions) {
        if (functions == null) {
            return;
        }
        
        for (ExpressionFunction function : functions) {
            try {
                registerFunction(function);
            } catch (Exception e) {
                LOGGER.error("Failed to register function: {}", function, e);
            }
        }
    }
    
    @Override
    public ExpressionFunction getFunction(String name) {
        return functions.get(name);
    }
    
    @Override
    public Map<String, ExpressionFunction> getAllFunctions() {
        return new ConcurrentHashMap<>(functions);
    }
    
    @Override
    public boolean hasFunction(String name) {
        return functions.containsKey(name);
    }
    
    @Override
    public boolean unregisterFunction(String name) {
        ExpressionFunction removed = functions.remove(name);
        if (removed != null) {
            LOGGER.info("Function '{}' unregistered successfully", name);
            return true;
        }
        return false;
    }
    
    @Override
    public void clear() {
        int count = functions.size();
        functions.clear();
        LOGGER.info("Cleared {} registered functions", count);
    }
    
    @Override
    public int size() {
        return functions.size();
    }
}
