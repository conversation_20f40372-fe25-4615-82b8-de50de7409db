package com.winit.workflow.engine.core.expression.function.custom;

import com.winit.workflow.engine.core.expression.function.ExpressionFunction;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Convert to string function
 * Converts any value to a string with optional formatting
 */
public class ToStringFunction implements ExpressionFunction {
    
    @Override
    public String getFunctionName() {
        return "toString";
    }
    
    @Override
    public String getDescription() {
        return "Converts any value to a string with optional formatting";
    }
    
    @Override
    public int getParameterCount() {
        return -1; // Variable parameters (1 or 2)
    }

    @Override
    public String getParameterInfo() {
        return "1-2"; // 1 or 2 parameters
    }
    
    @Override
    public int getPriority() {
        return 200; // Custom function priority
    }
    
    @Override
    public Object execute(Object... args) {
        if (args == null || args.length == 0 || args.length > 2) {
            throw new IllegalArgumentException("toString function requires 1 or 2 arguments");
        }
        
        Object value = args[0];
        String format = args.length > 1 ? args[1].toString() : null;
        
        if (value == null) {
            return "null";
        }
        
        try {
            if (format == null) {
                return value.toString();
            }
            
            // Apply formatting based on value type
            if (value instanceof Number) {
                DecimalFormat df = new DecimalFormat(format);
                return df.format(value);
            } else if (value instanceof Date) {
                SimpleDateFormat sdf = new SimpleDateFormat(format);
                return sdf.format(value);
            } else {
                // For other types, use String.format if format looks like a format string
                if (format.contains("%")) {
                    return String.format(format, value);
                } else {
                    // Just return the string representation
                    return value.toString();
                }
            }
        } catch (Exception e) {
            // If formatting fails, return plain string
            return value.toString();
        }
    }
    
    @Override
    public boolean validateParameters(Object... args) {
        return args != null && (args.length == 1 || args.length == 2);
    }
    
    @Override
    public String getExample() {
        return "${toString(123.456, '#.##')}\n// Result: \"123.46\" (number formatting)\n\n${toString(now(), 'yyyy-MM-dd')}\n// Result: \"2025-06-20\" (date formatting)\n\n${toString(value)}\n// Result: string representation of value";
    }
    
    @Override
    public String getFunctionSignature() {
        return "toString(value, format?): converts any value to a string\nParameters: value - the value to convert, format - optional format pattern";
    }
}
