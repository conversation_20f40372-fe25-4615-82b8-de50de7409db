package com.winit.workflow.engine.core.provider.smartengine;


import com.winit.workflow.engine.core.api.WorkflowEngine;
import com.winit.workflow.engine.core.api.WorkflowEngineConfig;
import com.winit.workflow.engine.core.api.WorkflowEngineFactory;
import com.winit.workflow.engine.core.spi.SpiProviderFactory;
import com.winit.workflow.engine.core.spi.WorkflowEngineProvider;

/**
 * SmartEngine提供者实现
 *
 * <AUTHOR>
 */
public class SmartEngineProvider implements WorkflowEngineProvider {

    @Override
    public String getType() {
        return "smart-engine";
    }

    @Override
    public WorkflowEngine createEngine(WorkflowEngineConfig config) {
        return WorkflowEngineFactory.getEngine(config);
    }

    @Override
    public String getDescription() {
        return "SmartEngine工作流引擎实现";
    }
} 