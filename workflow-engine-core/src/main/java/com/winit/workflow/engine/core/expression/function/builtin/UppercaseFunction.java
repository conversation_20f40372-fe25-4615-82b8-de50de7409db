package com.winit.workflow.engine.core.expression.function.builtin;

import com.winit.workflow.engine.core.expression.function.ExpressionFunction;

/**
 * 字符串转大写函数
 */
public class UppercaseFunction implements ExpressionFunction {
    
    @Override
    public String getFunctionName() {
        return "uppercase";
    }
    
    @Override
    public String getDescription() {
        return "Converts a string to uppercase";
    }
    
    @Override
    public int getParameterCount() {
        return 1;
    }
    
    @Override
    public int getPriority() {
        return 10; // 内置函数使用较高优先级
    }
    
    @Override
    public Object execute(Object... args) {
        if (args == null || args.length == 0) {
            return "";
        }
        
        Object arg = args[0];
        return arg != null ? arg.toString().toUpperCase() : "";
    }
    
    @Override
    public boolean validateParameters(Object... args) {
        return args != null && args.length == 1;
    }

    @Override
    public String getExample() {
        return "${uppercase('hello world')}\n// Result: \"HELLO WORLD\"\n\n${uppercase(username)}\n// Converts variable username to uppercase";
    }

    @Override
    public String getFunctionSignature() {
        return "uppercase(text): converts a string to uppercase\nParameters: text - the string to convert";
    }
}
