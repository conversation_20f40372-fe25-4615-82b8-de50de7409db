package com.winit.workflow.engine.core.provider.defaultengine;

import com.winit.workflow.engine.core.api.WorkflowEngineConfig;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.*;

/**
 * DefaultEngineProvider的单元测试
 */
public class DefaultEngineProviderTest {

    private DefaultEngineProvider provider;

    @Mock
    private WorkflowEngineConfig mockConfig;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        provider = DefaultEngineProvider.getInstance();
    }

    @Test
    public void testGetInstance() {
        // 验证单例模式
        DefaultEngineProvider instance1 = DefaultEngineProvider.getInstance();
        DefaultEngineProvider instance2 = DefaultEngineProvider.getInstance();

        assertNotNull(instance1);
        assertNotNull(instance2);
        assertSame(instance1, instance2);
    }

    @Test
    public void testGetType() {
        // 验证引擎类型
        String type = provider.getType();
        
        assertNotNull(type);
        assertEquals("default", type);
    }

    @Test
    public void testGetDescription() {
        // 验证引擎描述
        String description = provider.getDescription();
        
        assertNotNull(description);
        assertFalse(description.trim().isEmpty());
        assertTrue(description.contains("Default"));
        assertTrue(description.contains("fallback"));
    }

    @Test(expected = UnsupportedOperationException.class)
    public void testCreateEngineThrowsException() {
        // 验证创建引擎抛出异常
        provider.createEngine(mockConfig);
    }

    @Test(expected = UnsupportedOperationException.class)
    public void testCreateEngineWithNullConfigThrowsException() {
        // 验证null配置也抛出异常
        provider.createEngine(null);
    }

    @Test
    public void testCreateEngineExceptionMessage() {
        // 验证异常消息内容
        try {
            provider.createEngine(mockConfig);
            fail("Expected UnsupportedOperationException");
        } catch (UnsupportedOperationException e) {
            String message = e.getMessage();
            assertNotNull(message);
            assertTrue(message.contains("DefaultEngineProvider"));
            assertTrue(message.contains("fallback"));
            assertTrue(message.contains("SmartEngine"));
        }
    }

    @Test
    public void testSingletonThreadSafety() {
        // 测试多线程环境下的单例安全性
        final DefaultEngineProvider[] instances = new DefaultEngineProvider[10];
        
        Thread[] threads = new Thread[10];
        for (int i = 0; i < 10; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                instances[index] = DefaultEngineProvider.getInstance();
            });
        }

        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }

        // 等待所有线程完成
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                fail("Thread interrupted");
            }
        }

        // 验证所有实例都是同一个
        for (int i = 1; i < instances.length; i++) {
            assertSame(instances[0], instances[i]);
        }
    }

    @Test
    public void testProviderConsistency() {
        // 多次调用应该返回一致的结果
        String type1 = provider.getType();
        String type2 = provider.getType();
        String description1 = provider.getDescription();
        String description2 = provider.getDescription();

        assertEquals(type1, type2);
        assertEquals(description1, description2);
    }

    @Test
    public void testTypeIsNotEmpty() {
        // 验证类型不为空
        String type = provider.getType();
        
        assertNotNull(type);
        assertFalse(type.trim().isEmpty());
    }

    @Test
    public void testDescriptionIsNotEmpty() {
        // 验证描述不为空
        String description = provider.getDescription();
        
        assertNotNull(description);
        assertFalse(description.trim().isEmpty());
    }

    @Test
    public void testTypeFormat() {
        // 验证类型格式符合预期
        String type = provider.getType();
        
        assertEquals("default", type);
        assertFalse(type.contains(" "));
        assertFalse(type.contains("-"));
    }

    @Test
    public void testDescriptionContent() {
        // 验证描述内容包含关键信息
        String description = provider.getDescription();
        
        assertTrue(description.contains("Default"));
        assertTrue(description.contains("workflow"));
        assertTrue(description.contains("provider"));
        assertTrue(description.contains("fallback"));
    }
}
