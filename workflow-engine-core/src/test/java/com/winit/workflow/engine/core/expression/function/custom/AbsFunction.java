package com.winit.workflow.engine.core.expression.function.custom;

import com.winit.workflow.engine.core.expression.function.ExpressionFunction;

/**
 * Absolute value function
 * Returns the absolute value of a number
 */
public class AbsFunction implements ExpressionFunction {
    
    @Override
    public String getFunctionName() {
        return "abs";
    }
    
    @Override
    public String getDescription() {
        return "Returns the absolute value of a number";
    }
    
    @Override
    public int getParameterCount() {
        return 1;
    }
    
    @Override
    public int getPriority() {
        return 200; // Custom function priority
    }
    
    @Override
    public Object execute(Object... args) {
        if (args == null || args.length == 0) {
            throw new IllegalArgumentException("abs function requires one argument");
        }
        
        Object arg = args[0];
        if (arg == null) {
            return null;
        }
        
        try {
            if (arg instanceof Integer) {
                return Math.abs((Integer) arg);
            } else if (arg instanceof Long) {
                return Math.abs((Long) arg);
            } else if (arg instanceof Float) {
                return Math.abs((Float) arg);
            } else if (arg instanceof Double) {
                return Math.abs((Double) arg);
            } else {
                // Try to parse as double
                double value = Double.parseDouble(arg.toString());
                return Math.abs(value);
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Cannot convert argument to number: " + arg);
        }
    }
    
    @Override
    public boolean validateParameters(Object... args) {
        return args != null && args.length == 1;
    }
    
    @Override
    public String getExample() {
        return "${abs(-5)}\n// Result: 5\n\n${abs(-3.14)}\n// Result: 3.14\n\n${abs(value)}\n// Returns absolute value of variable";
    }
    
    @Override
    public String getFunctionSignature() {
        return "abs(number): returns the absolute value of a number\nParameters: number - the number to get absolute value of";
    }
}
