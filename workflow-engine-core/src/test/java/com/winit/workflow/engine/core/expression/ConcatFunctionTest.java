package com.winit.workflow.engine.core.expression;

import org.junit.Before;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * 专门测试concat函数的测试类
 */
public class ConcatFunctionTest {

    private ExpressionService expressionService;
    
    @Before
    public void setUp() {
        // 重置表达式服务，确保每个测试都使用新的实例
        ExpressionServiceFactory.reset();
        expressionService = ExpressionServiceFactory.getInstance();
        
        // 打印调试信息
        System.out.println("ExpressionService class: " + expressionService.getClass().getName());
        System.out.println("ExpressionEngine class: " + expressionService.getExpressionEngine().getClass().getName());
    }
    
    @Test
    public void testSimpleConcatWithString() {
        // 测试简单字符串连接
        Object result = expressionService.parseExpression("${concat('Hello', ' World')}", null);
        assertEquals("Hello World", result);
    }
    
    @Test
    public void testConcatWithVariable() {
        // 测试带变量的连接
        Map<String, Object> context = new HashMap<>();
        context.put("name", "张三");
        
        Object result = expressionService.parseExpression("${concat('Hello, ', name)}", context);
        assertEquals("Hello, 张三", result);
    }
    
    @Test
    public void testConcatWithNestedObject() {
        // 测试嵌套对象属性连接
        Map<String, Object> user = new HashMap<>();
        user.put("name", "李四");
        
        Map<String, Object> context = new HashMap<>();
        context.put("user", user);
        
        // 打印调试信息
        System.out.println("Context: " + context);
        System.out.println("User: " + user);
        
        Object result = expressionService.parseExpression("${concat('Hello, ', user.name)}", context);
        
        // 打印结果
        System.out.println("Result: " + result);
        
        assertEquals("Hello, 李四", result);
    }
    
    @Test
    public void testConcatWithMultipleArguments() {
        // 测试多参数连接
        Map<String, Object> context = new HashMap<>();
        context.put("firstName", "张");
        context.put("lastName", "三");
        
        Object result = expressionService.parseExpression(
            "${concat(firstName, ' ', lastName, ' 先生')}", context);
        assertEquals("张 三 先生", result);
    }
    
    @Test
    public void testConcatWithNullArgument() {
        // 测试包含null参数的连接
        Map<String, Object> context = new HashMap<>();
        context.put("name", null);
        
        Object result = expressionService.parseExpression("${concat('Hello, ', name)}", context);
        assertEquals("Hello, ", result);
    }
    
    @Test
    public void testDirectFunctionCall() {
        // 测试直接调用函数
        Map<String, Object> context = new HashMap<>();
        
        // 获取函数对象
        Object function = expressionService.getExpressionEngine().evaluate("concat", context);
        
        // 打印函数对象
        System.out.println("Function object: " + function);
        
        // 如果函数对象不为null，尝试直接调用
        if (function instanceof java.util.function.Function) {
            Object result = ((java.util.function.Function) function).apply(
                new Object[] {"Hello, ", "World"});
            assertEquals("Hello, World", result);
        }
    }
}