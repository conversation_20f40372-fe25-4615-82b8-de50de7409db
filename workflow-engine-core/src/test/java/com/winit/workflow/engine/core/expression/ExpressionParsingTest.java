package com.winit.workflow.engine.core.expression;

import org.junit.Before;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

public class ExpressionParsingTest {

    private ExpressionService expressionService;
    
    @Before
    public void setUp() {
        // 重置表达式服务，确保每个测试都使用新的实例
        ExpressionServiceFactory.reset();
        expressionService = ExpressionServiceFactory.getInstance();
    }
    
    @Test
    public void testSimpleExpression() {
        Map<String, Object> context = new HashMap<>();
        context.put("name", "张三");
        
        Object result = expressionService.parseExpression("${name}", context);
        assertEquals("张三", result);
    }
    
    @Test
    public void testConcatFunction() {
        Map<String, Object> context = new HashMap<>();
        context.put("name", "张三");
        
        Object result = expressionService.parseExpression("${concat('Hello, ', name)}", context);
        assertEquals("Hello, 张三", result);
    }
    
    @Test
    public void testNestedObject() {
        Map<String, Object> user = new HashMap<>();
        user.put("name", "李四");
        
        Map<String, Object> context = new HashMap<>();
        context.put("user", user);
        
        Object result = expressionService.parseExpression("${concat('Hello, ', user.name)}", context);
        assertEquals("Hello, 李四", result);
        
        // 直接打印调试信息
        System.out.println("Expression result: " + result);
        System.out.println("User object: " + user);
        System.out.println("User name: " + user.get("name"));
    }
    
    @Test
    public void testDirectPropertyAccess() {
        Map<String, Object> user = new HashMap<>();
        user.put("name", "李四");
        
        Map<String, Object> context = new HashMap<>();
        context.put("user", user);
        
        Object result = expressionService.parseExpression("${user.name}", context);
        assertEquals("李四", result);
    }
    
    @Test
    public void testExpressionWithMathOperation() {
        Map<String, Object> context = new HashMap<>();
        context.put("a", 5);
        context.put("b", 3);
        
        Object result = expressionService.parseExpression("${a + b}", context);
        assertEquals(8, result);
    }
}