package com.winit.workflow.engine.core.expression.function.custom;

import com.winit.workflow.engine.core.expression.function.ExpressionFunction;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Date comparison function
 * Compares two dates and returns comparison result (-1, 0, 1)
 */
public class CompareDateFunction implements ExpressionFunction {
    
    private static final String[] DATE_FORMATS = {
        "yyyy-MM-dd",
        "yyyy-MM-dd HH:mm:ss",
        "MM/dd/yyyy",
        "dd/MM/yyyy",
        "yyyy/MM/dd"
    };
    
    @Override
    public String getFunctionName() {
        return "compareDate";
    }
    
    @Override
    public String getDescription() {
        return "Compares two dates and returns comparison result (-1, 0, 1)";
    }
    
    @Override
    public int getParameterCount() {
        return 2;
    }
    
    @Override
    public int getPriority() {
        return 200; // Custom function priority
    }
    
    @Override
    public Object execute(Object... args) {
        if (args == null || args.length != 2) {
            throw new IllegalArgumentException("compareDate function requires two arguments");
        }
        
        Date date1 = parseDate(args[0]);
        Date date2 = parseDate(args[1]);
        
        if (date1 == null || date2 == null) {
            throw new IllegalArgumentException("Cannot parse date arguments");
        }
        
        return date1.compareTo(date2);
    }
    
    private Date parseDate(Object obj) {
        if (obj == null) {
            return null;
        }
        
        if (obj instanceof Date) {
            return (Date) obj;
        }
        
        String dateStr = obj.toString();
        for (String format : DATE_FORMATS) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(format);
                return sdf.parse(dateStr);
            } catch (ParseException e) {
                // Try next format
            }
        }
        
        return null;
    }
    
    @Override
    public boolean validateParameters(Object... args) {
        return args != null && args.length == 2;
    }
    
    @Override
    public String getExample() {
        return "${compareDate('2025-01-01', '2025-01-02')}\n// Result: -1 (first date is earlier)\n\n${compareDate('2025-01-01', '2025-01-01')}\n// Result: 0 (dates are equal)\n\n${compareDate('2025-01-02', '2025-01-01')}\n// Result: 1 (first date is later)";
    }
    
    @Override
    public String getFunctionSignature() {
        return "compareDate(date1, date2): compares two dates and returns -1, 0, or 1\nParameters: date1, date2 - dates to compare (supports various formats)";
    }
}
