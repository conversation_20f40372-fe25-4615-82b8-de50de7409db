package com.winit.workflow.engine.core.dispacter;

import com.alibaba.smart.framework.engine.constant.AssigneeTypeConstant;
import com.alibaba.smart.framework.engine.model.instance.TaskAssigneeCandidateInstance;
import com.winit.workflow.engine.core.api.WorkflowTaskAssigneeDispatcher;
import com.winit.workflow.engine.core.api.model.WorkflowTaskAssigneeCandidateInstance;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by 高海军 帝奇 74394 on 2017 January  18:03.
 */

public class DefaultTaskAssigneeDispatcher implements WorkflowTaskAssigneeDispatcher {


    @Override
    public List<WorkflowTaskAssigneeCandidateInstance> dispatch(Map<String, Object> input) {
        List<TaskAssigneeCandidateInstance> taskAssigneeCandidateInstanceList = new ArrayList();

        TaskAssigneeCandidateInstance taskAssigneeCandidateInstance = new TaskAssigneeCandidateInstance();
        taskAssigneeCandidateInstance.setAssigneeId("1");
        taskAssigneeCandidateInstance.setAssigneeType(AssigneeTypeConstant.USER);
        taskAssigneeCandidateInstanceList.add(taskAssigneeCandidateInstance);

        TaskAssigneeCandidateInstance taskAssigneeCandidateInstance1 = new TaskAssigneeCandidateInstance();
        taskAssigneeCandidateInstance1.setAssigneeId("3");
        taskAssigneeCandidateInstance1.setAssigneeType(AssigneeTypeConstant.USER);
        taskAssigneeCandidateInstanceList.add(taskAssigneeCandidateInstance1);


        TaskAssigneeCandidateInstance taskAssigneeCandidateInstance2 = new TaskAssigneeCandidateInstance();
        taskAssigneeCandidateInstance2.setAssigneeId("5");
        taskAssigneeCandidateInstance2.setAssigneeType(AssigneeTypeConstant.USER);
        taskAssigneeCandidateInstanceList.add(taskAssigneeCandidateInstance2);


        return taskAssigneeCandidateInstanceList.stream().map(it -> {
            WorkflowTaskAssigneeCandidateInstance result = new WorkflowTaskAssigneeCandidateInstance();
            result.setAssigneeId(it.getAssigneeId());
            result.setAssigneeType(it.getAssigneeType());
            result.setPriority(it.getPriority());
            return result;
        }).collect(Collectors.toList());
    }
}
