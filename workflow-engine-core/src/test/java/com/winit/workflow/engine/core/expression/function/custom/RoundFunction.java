package com.winit.workflow.engine.core.expression.function.custom;

import com.winit.workflow.engine.core.expression.function.ExpressionFunction;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * Round number function
 * Rounds a number to specified decimal places
 */
public class RoundFunction implements ExpressionFunction {
    
    @Override
    public String getFunctionName() {
        return "round";
    }
    
    @Override
    public String getDescription() {
        return "Rounds a number to specified decimal places";
    }
    
    @Override
    public int getParameterCount() {
        return -1; // Variable parameters (1 or 2)
    }
    
    @Override
    public int getPriority() {
        return 200; // Custom function priority
    }
    
    @Override
    public Object execute(Object... args) {
        if (args == null || args.length == 0 || args.length > 2) {
            throw new IllegalArgumentException("round function requires 1 or 2 arguments");
        }
        
        Object numberArg = args[0];
        int decimals = args.length > 1 ? parseInteger(args[1]) : 0;
        
        if (numberArg == null) {
            return null;
        }
        
        try {
            double number = parseNumeric(numberArg);
            
            if (decimals < 0) {
                throw new IllegalArgumentException("Decimal places cannot be negative: " + decimals);
            }
            
            // Use BigDecimal for precise rounding
            BigDecimal bd = BigDecimal.valueOf(number);
            bd = bd.setScale(decimals, RoundingMode.HALF_UP);
            
            // Return as integer if no decimal places requested
            if (decimals == 0) {
                return bd.intValue();
            } else {
                return bd.doubleValue();
            }
            
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Cannot convert argument to number: " + numberArg);
        }
    }
    
    private double parseNumeric(Object obj) throws NumberFormatException {
        if (obj instanceof Number) {
            return ((Number) obj).doubleValue();
        } else {
            return Double.parseDouble(obj.toString());
        }
    }
    
    private int parseInteger(Object obj) {
        if (obj instanceof Number) {
            return ((Number) obj).intValue();
        } else {
            return Integer.parseInt(obj.toString());
        }
    }
    
    @Override
    public boolean validateParameters(Object... args) {
        return args != null && (args.length == 1 || args.length == 2);
    }
    
    @Override
    public String getExample() {
        return "${round(3.14159, 2)}\n// Result: 3.14\n\n${round(2.7)}\n// Result: 3 (default 0 decimal places)\n\n${round(value, decimals)}\n// Rounds variable to specified decimals";
    }
    
    @Override
    public String getFunctionSignature() {
        return "round(number, decimals?): rounds a number to specified decimal places\nParameters: number - the number to round, decimals - decimal places (default: 0)";
    }
}
