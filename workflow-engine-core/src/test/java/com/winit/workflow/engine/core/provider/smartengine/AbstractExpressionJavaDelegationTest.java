package com.winit.workflow.engine.core.provider.smartengine;

import com.alibaba.smart.framework.engine.constant.ExtensionElementsConstant;
import com.alibaba.smart.framework.engine.context.ExecutionContext;
import com.alibaba.smart.framework.engine.model.assembly.ExtensionElements;
import com.alibaba.smart.framework.engine.model.assembly.impl.AbstractTask;
import com.alibaba.smart.framework.engine.model.instance.ExecutionInstance;
import com.alibaba.smart.framework.engine.smart.PropertyCompositeKey;
import com.alibaba.smart.framework.engine.smart.PropertyCompositeValue;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * AbstractExpressionJavaDelegation的单元测试
 */
public class AbstractExpressionJavaDelegationTest {

    @Mock
    private ExecutionContext mockExecutionContext;

    @Mock
    private ExecutionInstance mockExecutionInstance;

    @Mock
    private AbstractTask mockAbstractTask;

    @Mock
    private ExtensionElements mockExtensionElements;

    private Map<String, Object> requestMap;
    private Map<String, Object> responseMap;
    private TestExpressionJavaDelegation delegation;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        // 准备请求和响应Map
        requestMap = new HashMap<>();
        responseMap = new HashMap<>();

        // 配置Mock对象行为
        when(mockExecutionContext.getRequest()).thenReturn(requestMap);
        when(mockExecutionContext.getResponse()).thenReturn(responseMap);
        when(mockExecutionContext.getExecutionInstance()).thenReturn(mockExecutionInstance);
        when(mockExecutionInstance.getProcessDefinitionActivityId()).thenReturn("test-activity-id");

        // 创建测试委托实例
        delegation = new TestExpressionJavaDelegation();
    }

    @Test
    public void testSuccessfulExecution() {
        // 准备测试数据
        requestMap.put("key1", "value1");
        requestMap.put("key2", "${concat('Hello, ', 'World')}");

        // 执行
        delegation.execute(mockExecutionContext);

        // 验证doExecute被调用
        assertTrue("doExecute should be called", delegation.isDoExecuteCalled());
        
        // 验证响应包含请求数据
        assertTrue("Response should contain request data", responseMap.containsKey("key1"));
    }

    @Test(expected = RuntimeException.class)
    public void testExecutionWithNullContext() {
        // 执行 - 应该抛出异常
        delegation.execute(null);
    }

    @Test(expected = RuntimeException.class)
    public void testExecutionWithNullRequest() {
        // 配置Mock对象 - request为null
        when(mockExecutionContext.getRequest()).thenReturn(null);

        // 执行 - 应该抛出异常
        delegation.execute(mockExecutionContext);
    }

    @Test(expected = RuntimeException.class)
    public void testExecutionWithNullResponse() {
        // 配置Mock对象 - response为null
        when(mockExecutionContext.getResponse()).thenReturn(null);

        // 执行 - 应该抛出异常
        delegation.execute(mockExecutionContext);
    }

    @Test
    public void testProcessValueWithString() {
        Map<String, Object> context = new HashMap<>();
        context.put("name", "World");

        // 测试普通字符串
        Object result1 = delegation.testProcessValue("Hello", context);
        assertEquals("Hello", result1);

        // 测试表达式字符串
        Object result2 = delegation.testProcessValue("${concat('Hello, ', name)}", context);
        assertEquals("Hello, World", result2);
    }

    @Test
    public void testProcessValueWithMap() {
        Map<String, Object> context = new HashMap<>();
        context.put("name", "World");

        Map<String, Object> originalMap = new HashMap<>();
        originalMap.put("greeting", "${concat('Hello, ', name)}");
        originalMap.put("number", 123);

        @SuppressWarnings("unchecked")
        Map<Object, Object> result = (Map<Object, Object>) delegation.testProcessValue(originalMap, context);

        assertEquals("Hello, World", result.get("greeting"));
        assertEquals(123, result.get("number"));
    }

    @Test
    public void testProcessValueWithList() {
        Map<String, Object> context = new HashMap<>();
        context.put("name", "World");

        List<Object> originalList = new ArrayList<>();
        originalList.add("${concat('Hello, ', name)}");
        originalList.add(123);
        originalList.add("static");

        @SuppressWarnings("unchecked")
        List<Object> result = (List<Object>) delegation.testProcessValue(originalList, context);

        assertEquals("Hello, World", result.get(0));
        assertEquals(123, result.get(1));
        assertEquals("static", result.get(2));
    }

    @Test
    public void testExtractPropertiesWithValidTask() {
        // 准备扩展元素数据
        Map<String, Object> decorationMap = new HashMap<>();
        Map<PropertyCompositeKey, PropertyCompositeValue> propertyMap = new HashMap<>();

        PropertyCompositeKey key1 = mock(PropertyCompositeKey.class);
        PropertyCompositeValue value1 = mock(PropertyCompositeValue.class);
        when(key1.getName()).thenReturn("prop1");
        when(value1.getValue()).thenReturn("value1");
        propertyMap.put(key1, value1);

        decorationMap.put(ExtensionElementsConstant.PROPERTIES, propertyMap);

        when(mockAbstractTask.getExtensionElements()).thenReturn(mockExtensionElements);
        when(mockExtensionElements.getDecorationMap()).thenReturn(decorationMap);
        when(mockExecutionContext.getBaseElement()).thenReturn(mockAbstractTask);

        // 执行
        Map<String, Object> properties = delegation.testExtractProperties(mockExecutionContext);

        // 验证
        assertEquals("value1", properties.get("prop1"));
    }

    @Test
    public void testProcessPropertiesExpressions() {
        Map<String, Object> properties = new HashMap<>();
        properties.put("greeting", "${concat('Hello, ', name)}");
        properties.put("number", 42);

        Map<String, Object> context = new HashMap<>();
        context.put("name", "World");

        Map<String, Object> result = delegation.testProcessPropertiesExpressions(properties, context);

        assertEquals("Hello, World", result.get("greeting"));
        assertEquals(42, result.get("number"));
    }

    @Test
    public void testExtractPropertiesWithNullBaseElement() {
        // 配置null baseElement
        when(mockExecutionContext.getBaseElement()).thenReturn(null);

        // 执行
        Map<String, Object> properties = delegation.testExtractProperties(mockExecutionContext);

        // 验证返回空Map
        assertTrue("Properties should be empty", properties.isEmpty());
    }

    @Test
    public void testGetActivityId() {
        String activityId = delegation.testGetActivityId(mockExecutionContext);
        assertEquals("test-activity-id", activityId);
    }

    @Test
    public void testGetExpressionService() {
        assertNotNull("ExpressionService should not be null", delegation.testGetExpressionService());
    }

    @Test
    public void testCompleteExpressionProcessingFlow() {
        // 准备包含表达式的请求数据
        requestMap.put("userName", "${concat('user_', userId)}");
        requestMap.put("userId", "123");

        // 准备包含表达式的属性数据
        Map<String, Object> decorationMap = new HashMap<>();
        Map<PropertyCompositeKey, PropertyCompositeValue> propertyMap = new HashMap<>();

        PropertyCompositeKey key1 = mock(PropertyCompositeKey.class);
        PropertyCompositeValue value1 = mock(PropertyCompositeValue.class);
        when(key1.getName()).thenReturn("greeting");
        when(value1.getValue()).thenReturn("${concat('Hello, ', userName)}");
        propertyMap.put(key1, value1);

        decorationMap.put(ExtensionElementsConstant.PROPERTIES, propertyMap);

        when(mockAbstractTask.getExtensionElements()).thenReturn(mockExtensionElements);
        when(mockExtensionElements.getDecorationMap()).thenReturn(decorationMap);
        when(mockExecutionContext.getBaseElement()).thenReturn(mockAbstractTask);

        // 执行
        delegation.execute(mockExecutionContext);

        // 验证doExecute被调用
        assertTrue("doExecute should be called", delegation.isDoExecuteCalled());

        // 验证请求中的表达式被处理
        assertEquals("user_123", requestMap.get("userName"));
        assertEquals("123", requestMap.get("userId"));

        // 验证属性中的表达式被处理并添加到请求和响应中
        assertEquals("Hello, user_123", requestMap.get("greeting"));
        assertEquals("Hello, user_123", responseMap.get("greeting"));
    }

    /**
     * 测试用的AbstractExpressionJavaDelegation实现
     */
    public static class TestExpressionJavaDelegation extends AbstractExpressionJavaDelegation {
        private boolean doExecuteCalled = false;

        @Override
        protected void doExecute(ExecutionContext executionContext, Map<String, Object> properties) {
            doExecuteCalled = true;
        }

        public boolean isDoExecuteCalled() {
            return doExecuteCalled;
        }

        // 暴露protected方法用于测试
        public Object testProcessValue(Object value, Map<String, Object> context) {
            return processValue(value, context);
        }

        public Map<String, Object> testExtractProperties(ExecutionContext executionContext) {
            return extractProperties(executionContext);
        }

        public String testGetActivityId(ExecutionContext executionContext) {
            return getActivityId(executionContext);
        }

        public Object testGetExpressionService() {
            return getExpressionService();
        }

        public Map<String, Object> testProcessPropertiesExpressions(Map<String, Object> properties, Map<String, Object> context) {
            return processPropertiesExpressions(properties, context);
        }
    }
}
