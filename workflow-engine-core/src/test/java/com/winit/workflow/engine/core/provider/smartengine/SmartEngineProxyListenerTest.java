package com.winit.workflow.engine.core.provider.smartengine;

import com.alibaba.smart.framework.engine.context.ExecutionContext;
import com.alibaba.smart.framework.engine.pvm.event.EventConstant;
import com.winit.workflow.engine.core.listener.ListenerService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * SmartEngineProxyListener的单元测试
 */
public class SmartEngineProxyListenerTest {

    @Mock
    private ListenerService mockListenerService;

    @Mock
    private ExecutionContext mockExecutionContext;

    @Mock
    private EventConstant mockEventConstant;

    private SmartEngineProxyListener proxyListener;
    private Map<String, Object> requestMap;
    private Map<String, Object> responseMap;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);

        // 准备测试数据
        requestMap = new HashMap<>();
        requestMap.put("key1", "value1");
        requestMap.put("key2", 123);

        responseMap = new HashMap<>();
        responseMap.put("result", "success");

        // 配置Mock对象
        when(mockExecutionContext.getRequest()).thenReturn(requestMap);
        when(mockExecutionContext.getResponse()).thenReturn(responseMap);
        when(mockEventConstant.name()).thenReturn("TEST_EVENT");

        // 创建代理监听器
        proxyListener = new SmartEngineProxyListener(mockListenerService);
    }

    @Test
    public void testExecuteWithValidParameters() {
        // 执行监听器
        proxyListener.execute(mockEventConstant, mockExecutionContext);

        // 验证ListenerService被正确调用
        verify(mockListenerService, times(1)).execute(
            eq("TEST_EVENT"),
            eq(requestMap),
            eq(responseMap)
        );
    }

    @Test
    public void testExecuteWithDifferentEventName() {
        // 配置不同的事件名称
        when(mockEventConstant.name()).thenReturn("ANOTHER_EVENT");

        // 执行监听器
        proxyListener.execute(mockEventConstant, mockExecutionContext);

        // 验证ListenerService被正确调用
        verify(mockListenerService, times(1)).execute(
            eq("ANOTHER_EVENT"),
            eq(requestMap),
            eq(responseMap)
        );
    }

    @Test
    public void testExecuteWithEmptyMaps() {
        // 配置空的Map
        Map<String, Object> emptyRequest = new HashMap<>();
        Map<String, Object> emptyResponse = new HashMap<>();
        
        when(mockExecutionContext.getRequest()).thenReturn(emptyRequest);
        when(mockExecutionContext.getResponse()).thenReturn(emptyResponse);

        // 执行监听器
        proxyListener.execute(mockEventConstant, mockExecutionContext);

        // 验证ListenerService被正确调用
        verify(mockListenerService, times(1)).execute(
            eq("TEST_EVENT"),
            eq(emptyRequest),
            eq(emptyResponse)
        );
    }

    @Test(expected = NullPointerException.class)
    public void testConstructorWithNullListenerService() {
        // 创建代理监听器时传入null应该抛出异常
        new SmartEngineProxyListener(null);
    }

    @Test
    public void testExecuteCallsListenerServiceOnce() {
        // 执行监听器
        proxyListener.execute(mockEventConstant, mockExecutionContext);

        // 验证ListenerService只被调用一次
        verify(mockListenerService, times(1)).execute(any(String.class), any(Map.class), any(Map.class));
    }

    @Test
    public void testExecuteWithNullEventName() {
        // 配置null事件名称
        when(mockEventConstant.name()).thenReturn(null);

        // 执行监听器
        proxyListener.execute(mockEventConstant, mockExecutionContext);

        // 验证ListenerService被调用，即使事件名称为null
        verify(mockListenerService, times(1)).execute(
            eq(null),
            eq(requestMap),
            eq(responseMap)
        );
    }

    @Test
    public void testExecuteWithComplexData() {
        // 准备复杂的测试数据
        Map<String, Object> complexRequest = new HashMap<>();
        complexRequest.put("stringValue", "test");
        complexRequest.put("intValue", 42);
        complexRequest.put("boolValue", true);
        complexRequest.put("nullValue", null);

        Map<String, Object> nestedMap = new HashMap<>();
        nestedMap.put("nested", "value");
        complexRequest.put("nestedMap", nestedMap);

        when(mockExecutionContext.getRequest()).thenReturn(complexRequest);

        // 执行监听器
        proxyListener.execute(mockEventConstant, mockExecutionContext);

        // 验证ListenerService被正确调用
        verify(mockListenerService, times(1)).execute(
            eq("TEST_EVENT"),
            eq(complexRequest),
            eq(responseMap)
        );
    }

    @Test
    public void testMultipleExecutions() {
        // 多次执行监听器
        proxyListener.execute(mockEventConstant, mockExecutionContext);
        proxyListener.execute(mockEventConstant, mockExecutionContext);
        proxyListener.execute(mockEventConstant, mockExecutionContext);

        // 验证ListenerService被调用三次
        verify(mockListenerService, times(3)).execute(
            eq("TEST_EVENT"),
            eq(requestMap),
            eq(responseMap)
        );
    }

    @Test
    public void testExecuteWithListenerServiceException() {
        // 配置ListenerService抛出异常
        doThrow(new RuntimeException("Test exception")).when(mockListenerService)
            .execute(any(String.class), any(Map.class), any(Map.class));

        // 执行监听器应该传播异常
        try {
            proxyListener.execute(mockEventConstant, mockExecutionContext);
        } catch (RuntimeException e) {
            assertEquals("Test exception", e.getMessage());
        }

        // 验证ListenerService被调用
        verify(mockListenerService, times(1)).execute(any(String.class), any(Map.class), any(Map.class));
    }
}
