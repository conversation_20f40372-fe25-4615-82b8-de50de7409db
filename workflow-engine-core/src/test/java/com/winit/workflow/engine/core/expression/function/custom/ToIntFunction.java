package com.winit.workflow.engine.core.expression.function.custom;

import com.winit.workflow.engine.core.expression.function.ExpressionFunction;

/**
 * Convert to integer function
 * Converts strings or numbers to integers
 */
public class ToIntFunction implements ExpressionFunction {
    
    @Override
    public String getFunctionName() {
        return "toInt";
    }
    
    @Override
    public String getDescription() {
        return "Converts a string or number to an integer";
    }
    
    @Override
    public int getParameterCount() {
        return 1;
    }
    
    @Override
    public int getPriority() {
        return 200; // Custom function priority
    }
    
    @Override
    public Object execute(Object... args) {
        if (args == null || args.length == 0) {
            throw new IllegalArgumentException("toInt function requires one argument");
        }
        
        Object arg = args[0];
        if (arg == null) {
            return null;
        }
        
        try {
            if (arg instanceof Integer) {
                return arg;
            } else if (arg instanceof Long) {
                long value = (Long) arg;
                if (value > Integer.MAX_VALUE || value < Integer.MIN_VALUE) {
                    throw new IllegalArgumentException("Value out of integer range: " + value);
                }
                return (int) value;
            } else if (arg instanceof Float) {
                return ((Float) arg).intValue();
            } else if (arg instanceof Double) {
                return ((Double) arg).intValue();
            } else if (arg instanceof Boolean) {
                return ((Boolean) arg) ? 1 : 0;
            } else {
                // Try to parse as integer
                String str = arg.toString().trim();
                
                // Handle decimal strings by truncating
                if (str.contains(".")) {
                    double doubleValue = Double.parseDouble(str);
                    return (int) doubleValue;
                }
                
                return Integer.parseInt(str);
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Cannot convert argument to integer: " + arg);
        }
    }
    
    @Override
    public boolean validateParameters(Object... args) {
        return args != null && args.length == 1;
    }
    
    @Override
    public String getExample() {
        return "${toInt('123')}\n// Result: 123\n\n${toInt(3.14)}\n// Result: 3 (truncated)\n\n${toInt(true)}\n// Result: 1 (boolean conversion)";
    }
    
    @Override
    public String getFunctionSignature() {
        return "toInt(value): converts a value to an integer\nParameters: value - the value to convert (string, number, or boolean)";
    }
}
