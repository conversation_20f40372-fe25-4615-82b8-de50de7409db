package com.winit.workflow.engine.core.expression.function;

import com.winit.workflow.engine.core.expression.ExpressionService;
import com.winit.workflow.engine.core.expression.ExpressionServiceFactory;
import org.junit.Before;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * 动态函数替换机制测试
 * 验证新的动态函数替换机制能够正确处理所有注册的函数
 */
public class DynamicFunctionReplacementTest {

    private ExpressionService expressionService;

    @Before
    public void setUp() {
        // 重置表达式服务以确保测试的独立性
        ExpressionServiceFactory.reset();
        expressionService = ExpressionServiceFactory.getInstance();
    }

    /**
     * 辅助方法：注册自定义函数到表达式引擎
     */
    private void registerCustomFunction(ExpressionFunction function) {
        try {
            // 通过反射获取MvelExpressionEngine并注册函数
            java.lang.reflect.Field engineField =
                com.winit.workflow.engine.core.expression.DefaultExpressionService.class
                    .getDeclaredField("expressionEngine");
            engineField.setAccessible(true);
            Object engine = engineField.get(expressionService);

            if (engine instanceof com.winit.workflow.engine.core.expression.provider.MvelExpressionEngine) {
                java.lang.reflect.Method registerMethod =
                    com.winit.workflow.engine.core.expression.provider.MvelExpressionEngine.class
                        .getDeclaredMethod("registerExpressionFunction", ExpressionFunction.class);
                registerMethod.setAccessible(true);
                registerMethod.invoke(engine, function);
            }
        } catch (Exception e) {
            fail("Failed to register custom function: " + e.getMessage());
        }
    }

    @Test
    public void testDynamicFunctionReplacementForBuiltinFunctions() {
        Map<String, Object> context = new HashMap<>();
        context.put("name", "张三");

        // 测试内置函数的动态替换
        Object result = expressionService.parseExpression("${concat('Hello, ', name)}", context);
        assertEquals("Hello, 张三", result);

        result = expressionService.parseExpression("${uppercase('hello')}", context);
        assertEquals("HELLO", result);

        result = expressionService.parseExpression("${lowercase('WORLD')}", context);
        assertEquals("world", result);

        result = expressionService.parseExpression("${now()}", context);
        assertNotNull(result);
        assertTrue(result instanceof java.util.Date);
    }

    @Test
    public void testDynamicFunctionReplacementForCustomFunctions() {
        Map<String, Object> context = new HashMap<>();

        // 先注册自定义函数用于测试
        ExpressionFunction calculateTaxFunction = new ExpressionFunction() {
            @Override
            public String getFunctionName() {
                return "calculateTax";
            }

            @Override
            public Object execute(Object... args) {
                if (args.length != 2) {
                    throw new IllegalArgumentException("calculateTax requires 2 arguments");
                }
                double amount = Double.parseDouble(args[0].toString());
                double rate = Double.parseDouble(args[1].toString());
                return amount * rate / 100.0;
            }

            @Override
            public int getParameterCount() {
                return 2;
            }
        };

        ExpressionFunction generateOrderNumberFunction = new ExpressionFunction() {
            @Override
            public String getFunctionName() {
                return "generateOrderNumber";
            }

            @Override
            public Object execute(Object... args) {
                return "ORD-" + System.currentTimeMillis();
            }

            @Override
            public int getParameterCount() {
                return 0;
            }
        };

        // 注册函数到引擎
        registerCustomFunction(calculateTaxFunction);
        registerCustomFunction(generateOrderNumberFunction);

        context.put("amount", 100.0);
        context.put("rate", 13.0);

        // 测试自定义函数calculateTax的动态替换
        Object result = expressionService.parseExpression("${calculateTax(amount, rate)}", context);
        assertNotNull("calculateTax function should return a result", result);
        assertTrue("Result should be a number", result instanceof Number);
        assertEquals("Tax calculation should be correct", 13.0, ((Number) result).doubleValue(), 0.01);

        // 测试自定义函数generateOrderNumber的动态替换
        result = expressionService.parseExpression("${generateOrderNumber()}", context);
        assertNotNull("generateOrderNumber function should return a result", result);
        assertTrue("Result should be a string", result instanceof String);
        assertTrue("Order number should start with ORD-", result.toString().startsWith("ORD-"));
    }

    @Test
    public void testMixedFunctionCalls() {
        Map<String, Object> context = new HashMap<>();

        // 先注册自定义函数用于测试
        ExpressionFunction calculateTaxFunction = new ExpressionFunction() {
            @Override
            public String getFunctionName() {
                return "calculateTax";
            }

            @Override
            public Object execute(Object... args) {
                double amount = Double.parseDouble(args[0].toString());
                double rate = Double.parseDouble(args[1].toString());
                return amount * rate / 100.0;
            }

            @Override
            public int getParameterCount() {
                return 2;
            }
        };

        ExpressionFunction generateOrderNumberFunction = new ExpressionFunction() {
            @Override
            public String getFunctionName() {
                return "generateOrderNumber";
            }

            @Override
            public Object execute(Object... args) {
                return "ORD-12345";  // 使用固定值便于测试
            }

            @Override
            public int getParameterCount() {
                return 0;
            }
        };

        // 注册函数到引擎
        registerCustomFunction(calculateTaxFunction);
        registerCustomFunction(generateOrderNumberFunction);

        context.put("amount", 100.0);
        context.put("rate", 13.0);

        // 测试混合函数调用
        Object result = expressionService.parseExpression(
            "${concat('Tax: ', calculateTax(amount, rate))}", context);
        assertEquals("Tax: 13.0", result);

        result = expressionService.parseExpression(
            "${uppercase(concat('order-', generateOrderNumber()))}", context);
        assertNotNull(result);
        assertEquals("ORDER-ORD-12345", result);
    }

    @Test
    public void testDynamicFunctionRegistration() {
        // 测试运行时注册新函数
        ExpressionFunction customFunction = new ExpressionFunction() {
            @Override
            public String getFunctionName() {
                return "multiply";
            }

            @Override
            public Object execute(Object... args) {
                if (args.length != 2) {
                    throw new IllegalArgumentException("multiply function requires 2 arguments");
                }
                double a = Double.parseDouble(args[0].toString());
                double b = Double.parseDouble(args[1].toString());
                return a * b;
            }

            @Override
            public int getParameterCount() {
                return 2;
            }
        };

        // 注册新函数
        registerCustomFunction(customFunction);

        // 测试新注册的函数
        Map<String, Object> context = new HashMap<>();
        context.put("a", 5);
        context.put("b", 3);

        Object result = expressionService.parseExpression("${multiply(a, b)}", context);
        assertEquals(15.0, ((Number) result).doubleValue(), 0.01);
    }

    @Test
    public void testFunctionNameValidation() {
        Map<String, Object> context = new HashMap<>();

        // 测试不存在的函数
        try {
            expressionService.parseExpression("${nonExistentFunction()}", context);
            fail("Should throw exception for non-existent function");
        } catch (Exception e) {
            assertTrue("Should contain function name in error message", 
                e.getMessage().contains("nonExistentFunction") || 
                e.getCause().getMessage().contains("nonExistentFunction"));
        }
    }
}
