package com.winit.workflow.engine.core.expression.function;

import com.winit.workflow.engine.core.expression.ExpressionService;
import com.winit.workflow.engine.core.expression.ExpressionServiceFactory;
import org.junit.Before;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * 自动函数注册机制测试
 */
public class AutoFunctionRegistrationTest {

    private ExpressionService expressionService;

    @Before
    public void setUp() {
        // 重置表达式服务以确保测试的独立性
        ExpressionServiceFactory.reset();
        expressionService = ExpressionServiceFactory.getInstance();
    }

    @Test
    public void testBuiltinFunctionsAutoRegistered() {
        Map<String, Object> context = new HashMap<>();
        context.put("name", "张三");

        // 测试concat函数
        Object result = expressionService.parseExpression("${concat('Hello, ', name)}", context);
        assertEquals("Hello, 张三", result);

        // 测试uppercase函数
        result = expressionService.parseExpression("${uppercase('hello')}", context);
        assertEquals("HELLO", result);

        // 测试lowercase函数
        result = expressionService.parseExpression("${lowercase('WORLD')}", context);
        assertEquals("world", result);

        // 测试now函数
        result = expressionService.parseExpression("${now()}", context);
        assertNotNull(result);
        assertTrue(result instanceof java.util.Date);
    }

    @Test
    public void testConcatFunctionWithMultipleParameters() {
        Map<String, Object> context = new HashMap<>();
        context.put("firstName", "张");
        context.put("lastName", "三");

        Object result = expressionService.parseExpression(
            "${concat(firstName, lastName, ' 先生')}", context);
        assertEquals("张三 先生", result);
    }

    @Test
    public void testConcatFunctionWithNullParameters() {
        Map<String, Object> context = new HashMap<>();
        context.put("name", null);

        Object result = expressionService.parseExpression("${concat('Hello, ', name)}", context);
        assertEquals("Hello, ", result);
    }

    @Test
    public void testFunctionDiscovery() {
        // 测试函数发现机制
        java.util.List<ExpressionFunction> functions = FunctionDiscovery.discoverFunctions();
        
        assertNotNull("Functions list should not be null", functions);
        assertTrue("Should discover at least 4 built-in functions", functions.size() >= 4);
        
        // 验证内置函数是否被发现
        boolean foundConcat = false;
        boolean foundUppercase = false;
        boolean foundLowercase = false;
        boolean foundNow = false;
        
        for (ExpressionFunction function : functions) {
            String name = function.getFunctionName();
            switch (name) {
                case "concat":
                    foundConcat = true;
                    break;
                case "uppercase":
                    foundUppercase = true;
                    break;
                case "lowercase":
                    foundLowercase = true;
                    break;
                case "now":
                    foundNow = true;
                    break;
            }
        }
        
        assertTrue("concat function should be discovered", foundConcat);
        assertTrue("uppercase function should be discovered", foundUppercase);
        assertTrue("lowercase function should be discovered", foundLowercase);
        assertTrue("now function should be discovered", foundNow);
    }

    @Test
    public void testFunctionRegistry() {
        FunctionRegistry registry = new DefaultFunctionRegistry();
        
        // 测试注册自定义函数
        ExpressionFunction testFunction = new ExpressionFunction() {
            @Override
            public String getFunctionName() {
                return "test";
            }
            
            @Override
            public Object execute(Object... args) {
                return "test result";
            }
        };
        
        registry.registerFunction(testFunction);
        
        assertTrue("Function should be registered", registry.hasFunction("test"));
        assertEquals("Should have 1 function", 1, registry.size());
        
        ExpressionFunction retrieved = registry.getFunction("test");
        assertNotNull("Retrieved function should not be null", retrieved);
        assertEquals("Function names should match", "test", retrieved.getFunctionName());
        
        // 测试取消注册
        boolean unregistered = registry.unregisterFunction("test");
        assertTrue("Function should be unregistered", unregistered);
        assertFalse("Function should no longer exist", registry.hasFunction("test"));
        assertEquals("Should have 0 functions", 0, registry.size());
    }
}
