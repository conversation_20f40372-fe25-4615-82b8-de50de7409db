package com.winit.workflow.engine.core.expression.function.custom;

import com.winit.workflow.engine.core.expression.function.ExpressionFunction;

import java.util.HashMap;
import java.util.Map;

/**
 * Length unit conversion function
 * Converts between different length units (meters, feet, inches, centimeters)
 */
public class ConvertLengthFunction implements ExpressionFunction {
    
    // Conversion factors to meters
    private static final Map<String, Double> TO_METERS = new HashMap<>();
    
    static {
        TO_METERS.put("m", 1.0);
        TO_METERS.put("meter", 1.0);
        TO_METERS.put("meters", 1.0);
        TO_METERS.put("cm", 0.01);
        TO_METERS.put("centimeter", 0.01);
        TO_METERS.put("centimeters", 0.01);
        TO_METERS.put("ft", 0.3048);
        TO_METERS.put("foot", 0.3048);
        TO_METERS.put("feet", 0.3048);
        TO_METERS.put("in", 0.0254);
        TO_METERS.put("inch", 0.0254);
        TO_METERS.put("inches", 0.0254);
    }
    
    @Override
    public String getFunctionName() {
        return "convertLength";
    }
    
    @Override
    public String getDescription() {
        return "Converts between different length units (meters, feet, inches, centimeters)";
    }
    
    @Override
    public int getParameterCount() {
        return 3;
    }
    
    @Override
    public int getPriority() {
        return 200; // Custom function priority
    }
    
    @Override
    public Object execute(Object... args) {
        if (args == null || args.length != 3) {
            throw new IllegalArgumentException("convertLength function requires three arguments: value, fromUnit, toUnit");
        }
        
        double value;
        try {
            value = Double.parseDouble(args[0].toString());
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("First argument must be a number: " + args[0]);
        }
        
        String fromUnit = args[1].toString().toLowerCase();
        String toUnit = args[2].toString().toLowerCase();
        
        Double fromFactor = TO_METERS.get(fromUnit);
        Double toFactor = TO_METERS.get(toUnit);
        
        if (fromFactor == null) {
            throw new IllegalArgumentException("Unsupported from unit: " + fromUnit + 
                ". Supported units: m, cm, ft, in");
        }
        
        if (toFactor == null) {
            throw new IllegalArgumentException("Unsupported to unit: " + toUnit + 
                ". Supported units: m, cm, ft, in");
        }
        
        // Convert to meters first, then to target unit
        double meters = value * fromFactor;
        double result = meters / toFactor;
        
        return result;
    }
    
    @Override
    public boolean validateParameters(Object... args) {
        return args != null && args.length == 3;
    }
    
    @Override
    public String getExample() {
        return "${convertLength(1, 'm', 'ft')}\n// Result: 3.28084 (1 meter to feet)\n\n${convertLength(12, 'in', 'cm')}\n// Result: 30.48 (12 inches to centimeters)\n\n${convertLength(100, 'cm', 'm')}\n// Result: 1.0 (100 centimeters to meters)";
    }
    
    @Override
    public String getFunctionSignature() {
        return "convertLength(value, fromUnit, toUnit): converts between length units\nParameters: value - numeric value, fromUnit - source unit, toUnit - target unit\nSupported units: m, cm, ft, in";
    }
}
