package com.winit.workflow.engine.core.expression;

import com.winit.workflow.engine.core.expression.provider.MvelExpressionEngine;
import org.junit.Before;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * 自定义表达式函数执行测试
 * 验证所有自定义函数在表达式引擎中的实际执行效果
 */
public class CustomFunctionExecutionTest {
    
    private ExpressionService expressionService;
    
    @Before
    public void setUp() {
        // 创建表达式引擎和服务
        ExpressionEngine engine = new MvelExpressionEngine();
        expressionService = new DefaultExpressionService(engine);
    }
    
    // ==================== abs 函数测试 ====================
    
    @Test
    public void testAbsFunctionWithPositiveNumber() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${abs(5)}", context);
        assertEquals(5, result);
    }
    
    @Test
    public void testAbsFunctionWithNegativeNumber() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${abs(-3.14)}", context);
        assertEquals(3.14, result);
    }
    
    @Test
    public void testAbsFunctionWithZero() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${abs(0)}", context);
        assertEquals(0, result);
    }
    
    @Test
    public void testAbsFunctionWithVariable() {
        Map<String, Object> context = new HashMap<>();
        context.put("value", -42);
        Object result = expressionService.parseExpression("${abs(value)}", context);
        assertEquals(42, result);
    }
    
    @Test
    public void testAbsFunctionWithStringNumber() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${abs('-7.5')}", context);
        assertEquals(7.5, result);
    }
    
    // ==================== compareDate 函数测试 ====================
    
    @Test
    public void testCompareDateWithEarlierDate() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${compareDate('2025-01-01', '2025-01-02')}", context);
        assertEquals(-1, result);
    }
    
    @Test
    public void testCompareDateWithEqualDates() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${compareDate('2025-01-01', '2025-01-01')}", context);
        assertEquals(0, result);
    }
    
    @Test
    public void testCompareDateWithLaterDate() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${compareDate('2025-01-02', '2025-01-01')}", context);
        assertEquals(1, result);
    }
    
    @Test
    public void testCompareDateWithDifferentFormats() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${compareDate('01/01/2025', '2025-01-02')}", context);
        assertEquals(-1, result);
    }
    
    // ==================== convertLength 函数测试 ====================
    
    @Test
    public void testConvertLengthMeterToFeet() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${convertLength(1, 'm', 'ft')}", context);
        assertEquals(3.28084, (Double) result, 0.00001);
    }
    
    @Test
    public void testConvertLengthInchesToCentimeters() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${convertLength(12, 'in', 'cm')}", context);
        assertEquals(30.48, (Double) result, 0.01);
    }
    
    @Test
    public void testConvertLengthCentimetersToMeters() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${convertLength(100, 'cm', 'm')}", context);
        assertEquals(1.0, (Double) result, 0.001);
    }
    
    @Test
    public void testConvertLengthWithVariables() {
        Map<String, Object> context = new HashMap<>();
        context.put("length", 5);
        context.put("fromUnit", "ft");
        context.put("toUnit", "m");
        Object result = expressionService.parseExpression("${convertLength(length, fromUnit, toUnit)}", context);
        assertEquals(1.524, (Double) result, 0.001);
    }
    
    // ==================== toInt 函数测试 ====================
    
    @Test
    public void testToIntWithStringNumber() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${toInt('123')}", context);
        assertEquals(123, result);
    }
    
    @Test
    public void testToIntWithDouble() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${toInt(3.14)}", context);
        assertEquals(3, result);
    }
    
    @Test
    public void testToIntWithBoolean() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${toInt(true)}", context);
        assertEquals(1, result);
        
        result = expressionService.parseExpression("${toInt(false)}", context);
        assertEquals(0, result);
    }
    
    @Test
    public void testToIntWithVariable() {
        Map<String, Object> context = new HashMap<>();
        context.put("value", "456");
        Object result = expressionService.parseExpression("${toInt(value)}", context);
        assertEquals(456, result);
    }
    
    // ==================== toString 函数测试 ====================
    
    @Test
    public void testToStringWithNumber() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${toString(123)}", context);
        assertEquals("123", result);
    }
    
    @Test
    public void testToStringWithNumberFormatting() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${toString(123.456, '#.##')}", context);
        assertEquals("123.46", result);
    }
    
    @Test
    public void testToStringWithVariable() {
        Map<String, Object> context = new HashMap<>();
        context.put("value", 789);
        Object result = expressionService.parseExpression("${toString(value)}", context);
        assertEquals("789", result);
    }
    
    // ==================== max 函数测试 ====================
    
    @Test
    public void testMaxWithMultipleNumbers() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${max(1, 5, 3, 9, 2)}", context);
        assertEquals(9, result);
    }
    
    @Test
    public void testMaxWithDecimals() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${max(3.14, 2.71, 1.41)}", context);
        assertEquals(3.14, result);
    }
    
    @Test
    public void testMaxWithVariables() {
        Map<String, Object> context = new HashMap<>();
        context.put("a", 10);
        context.put("b", 20);
        context.put("c", 15);
        Object result = expressionService.parseExpression("${max(a, b, c)}", context);
        assertEquals(20, result);
    }
    
    @Test
    public void testMaxWithSingleValue() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${max(42)}", context);
        assertEquals(42, result);
    }
    
    // ==================== min 函数测试 ====================
    
    @Test
    public void testMinWithMultipleNumbers() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${min(1, 5, 3, 9, 2)}", context);
        assertEquals(1, result);
    }
    
    @Test
    public void testMinWithDecimals() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${min(3.14, 2.71, 1.41)}", context);
        assertEquals(1.41, result);
    }
    
    @Test
    public void testMinWithVariables() {
        Map<String, Object> context = new HashMap<>();
        context.put("a", 10);
        context.put("b", 5);
        context.put("c", 15);
        Object result = expressionService.parseExpression("${min(a, b, c)}", context);
        assertEquals(5, result);
    }
    
    // ==================== round 函数测试 ====================
    
    @Test
    public void testRoundWithDecimals() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${round(3.14159, 2)}", context);
        assertEquals(3.14, result);
    }
    
    @Test
    public void testRoundToInteger() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${round(2.7)}", context);
        assertEquals(3, result);
    }
    
    @Test
    public void testRoundWithVariable() {
        Map<String, Object> context = new HashMap<>();
        context.put("value", 5.6789);
        context.put("decimals", 3);
        Object result = expressionService.parseExpression("${round(value, decimals)}", context);
        assertEquals(5.679, result);
    }
    
    // ==================== substring 函数测试 ====================
    
    @Test
    public void testSubstringWithStartAndEnd() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${substring('Hello World', 0, 5)}", context);
        assertEquals("Hello", result);
    }
    
    @Test
    public void testSubstringFromIndex() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${substring('Hello World', 6)}", context);
        assertEquals("World", result);
    }
    
    @Test
    public void testSubstringWithVariable() {
        Map<String, Object> context = new HashMap<>();
        context.put("text", "Programming");
        Object result = expressionService.parseExpression("${substring(text, 0, 7)}", context);
        assertEquals("Program", result);
    }
    
    @Test
    public void testSubstringWithNegativeIndex() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${substring('Hello World', -5)}", context);
        assertEquals("World", result);
    }
    
    // ==================== contains 函数测试 ====================
    
    @Test
    public void testContainsTrue() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${contains('Hello World', 'World')}", context);
        assertEquals(true, result);
    }
    
    @Test
    public void testContainsFalse() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${contains('Hello World', 'world')}", context);
        assertEquals(false, result); // Case sensitive
    }
    
    @Test
    public void testContainsWithVariable() {
        Map<String, Object> context = new HashMap<>();
        context.put("text", "Java Programming");
        context.put("search", "Program");
        Object result = expressionService.parseExpression("${contains(text, search)}", context);
        assertEquals(true, result);
    }
    
    @Test
    public void testContainsEmptyString() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${contains('Hello', '')}", context);
        assertEquals(true, result);
    }

    // ==================== 复合表达式测试 ====================

    @Test
    public void testComplexExpressionWithMultipleFunctions() {
        Map<String, Object> context = new HashMap<>();
        context.put("value1", -5.7);
        context.put("value2", 3.2);
        // 使用 abs, max, round 函数的组合
        Object result = expressionService.parseExpression("${round(max(abs(value1), abs(value2)), 1)}", context);
        assertEquals(5.7, result);
    }

    @Test
    public void testNestedStringFunctions() {
        Map<String, Object> context = new HashMap<>();
        context.put("text", "Hello World Programming");
        // 使用 substring 和 contains 的组合
        Object result = expressionService.parseExpression("${contains(substring(text, 0, 11), 'World')}", context);
        assertEquals(true, result);
    }

    @Test
    public void testMathematicalExpression() {
        Map<String, Object> context = new HashMap<>();
        context.put("a", 10);
        context.put("b", 20);
        context.put("c", 15);
        // 计算平均值并四舍五入
        Object result = expressionService.parseExpression("${round((a + b + c) / 3, 2)}", context);
        assertEquals(15.0, result);
    }

    @Test
    public void testConditionalWithFunctions() {
        Map<String, Object> context = new HashMap<>();
        context.put("score", 85.7);
        // 根据分数判断等级
        Object result = expressionService.parseExpression("${round(score) >= 90 ? 'A' : (round(score) >= 80 ? 'B' : 'C')}", context);
        assertEquals("B", result);
    }

    @Test
    public void testStringProcessingChain() {
        Map<String, Object> context = new HashMap<>();
        context.put("name", "  John Doe  ");
        // 字符串处理链：截取、转换、检查
        Object result = expressionService.parseExpression("${contains(toString(substring(name.trim(), 0, 4)), 'John')}", context);
        assertEquals(true, result);
    }

    // ==================== 边界条件和错误处理测试 ====================

    @Test
    public void testAbsWithNull() {
        Map<String, Object> context = new HashMap<>();
        context.put("value", null);
        try {
            expressionService.parseExpression("${abs(value)}", context);
            fail("Should throw exception for null value");
        } catch (Exception e) {
            // Expected exception - abs function should handle null appropriately
            assertTrue("Exception should mention expression parsing failure",
                e.getMessage().contains("Expression parsing failed") ||
                e.getMessage().contains("Expression evaluation failed"));
        }
    }

    @Test
    public void testMaxWithEmptyArguments() {
        Map<String, Object> context = new HashMap<>();
        try {
            expressionService.parseExpression("${max()}", context);
            fail("Should throw exception for empty arguments");
        } catch (Exception e) {
            // Expected exception
            assertTrue("Exception should mention expression parsing failure",
                e.getMessage().contains("Expression parsing failed") ||
                e.getMessage().contains("Expression evaluation failed"));
        }
    }

    @Test
    public void testConvertLengthWithInvalidUnit() {
        Map<String, Object> context = new HashMap<>();
        try {
            expressionService.parseExpression("${convertLength(1, 'invalid', 'm')}", context);
            fail("Should throw exception for invalid unit");
        } catch (Exception e) {
            // Expected exception
            assertTrue("Exception should mention expression parsing failure",
                e.getMessage().contains("Expression parsing failed") ||
                e.getMessage().contains("Expression evaluation failed"));
        }
    }

    @Test
    public void testToIntWithInvalidString() {
        Map<String, Object> context = new HashMap<>();
        try {
            expressionService.parseExpression("${toInt('not_a_number')}", context);
            fail("Should throw exception for invalid number string");
        } catch (Exception e) {
            // Expected exception
            assertTrue("Exception should mention expression parsing failure",
                e.getMessage().contains("Expression parsing failed") ||
                e.getMessage().contains("Expression evaluation failed"));
        }
    }

    @Test
    public void testSubstringWithInvalidIndices() {
        Map<String, Object> context = new HashMap<>();
        // 测试超出范围的索引 - 应该被自动调整而不是抛出异常
        Object result = expressionService.parseExpression("${substring('Hello', 0, 100)}", context);
        assertEquals("Hello", result);
    }

    @Test
    public void testRoundWithNegativeDecimals() {
        Map<String, Object> context = new HashMap<>();
        try {
            expressionService.parseExpression("${round(3.14, -1)}", context);
            fail("Should throw exception for negative decimal places");
        } catch (Exception e) {
            // Expected exception
            assertTrue("Exception should mention expression parsing failure",
                e.getMessage().contains("Expression parsing failed") ||
                e.getMessage().contains("Expression evaluation failed"));
        }
    }

    @Test
    public void testCompareDateWithInvalidFormat() {
        Map<String, Object> context = new HashMap<>();
        try {
            expressionService.parseExpression("${compareDate('invalid_date', '2025-01-01')}", context);
            fail("Should throw exception for invalid date format");
        } catch (Exception e) {
            // Expected exception
            assertTrue("Exception should mention expression parsing failure",
                e.getMessage().contains("Expression parsing failed") ||
                e.getMessage().contains("Expression evaluation failed"));
        }
    }

    // ==================== 性能和边界值测试 ====================

    @Test
    public void testLargeNumberOperations() {
        Map<String, Object> context = new HashMap<>();
        context.put("largeNumber", 1000000.123456789);
        Object result = expressionService.parseExpression("${round(abs(largeNumber), 2)}", context);
        assertEquals(1000000.12, result);
    }

    @Test
    public void testLongStringOperations() {
        Map<String, Object> context = new HashMap<>();
        StringBuilder longString = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longString.append("test");
        }
        context.put("longText", longString.toString());

        Object result = expressionService.parseExpression("${contains(longText, 'test')}", context);
        assertEquals(true, result);

        result = expressionService.parseExpression("${substring(longText, 0, 4)}", context);
        assertEquals("test", result);
    }

    @Test
    public void testMaxMinWithManyArguments() {
        Map<String, Object> context = new HashMap<>();
        Object result = expressionService.parseExpression("${max(1,2,3,4,5,6,7,8,9,10,11,12,13,14,15)}", context);
        assertEquals(15, result);

        result = expressionService.parseExpression("${min(15,14,13,12,11,10,9,8,7,6,5,4,3,2,1)}", context);
        assertEquals(1, result);
    }

    // ==================== 类型转换和兼容性测试 ====================

    @Test
    public void testMixedTypeOperations() {
        Map<String, Object> context = new HashMap<>();
        context.put("intValue", 42);
        context.put("doubleValue", 3.14);
        context.put("stringValue", "100");

        // 测试不同类型的混合运算
        Object result = expressionService.parseExpression("${max(intValue, doubleValue, toInt(stringValue))}", context);
        assertEquals(100, result);
    }

    @Test
    public void testStringNumberConversions() {
        Map<String, Object> context = new HashMap<>();
        context.put("numberString", "123.45");

        Object result = expressionService.parseExpression("${round(abs(numberString), 1)}", context);
        assertEquals(123.5, result);
    }

    @Test
    public void testBooleanConversions() {
        Map<String, Object> context = new HashMap<>();
        context.put("flag", true);

        Object result = expressionService.parseExpression("${toString(toInt(flag))}", context);
        assertEquals("1", result);
    }
}
