package com.winit.workflow.engine.core.expression;

import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

public class ExpressionServiceTest {

    @Test
    public void testExpressionServiceFactory() {
        ExpressionService service = ExpressionServiceFactory.getInstance();
        assertNotNull("ExpressionService should not be null", service);
        
        ExpressionEngine engine = service.getExpressionEngine();
        assertNotNull("ExpressionEngine should not be null", engine);
    }
    
    @Test
    public void testConcatFunction() {
        ExpressionService service = ExpressionServiceFactory.getInstance();
        
        Map<String, Object> context = new HashMap<>();
        context.put("name", "张三");
        
        Object result = service.parseExpression("${concat('Hello, ', name)}", context);
        assertEquals("Hello, 张三", result);
    }
    
    @Test
    public void testNestedObject() {
        ExpressionService service = ExpressionServiceFactory.getInstance();
        
        Map<String, Object> user = new HashMap<>();
        user.put("name", "李四");
        
        Map<String, Object> context = new HashMap<>();
        context.put("user", user);
        
        Object result = service.parseExpression("${concat('Hello, ', user.name)}", context);
        assertEquals("Hello, 李四", result);
    }
}