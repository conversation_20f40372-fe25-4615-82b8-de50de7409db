package com.winit.workflow.engine.core.provider.smartengine;

import com.alibaba.smart.framework.engine.configuration.InstanceAccessor;
import com.alibaba.smart.framework.engine.context.ExecutionContext;
import com.alibaba.smart.framework.engine.model.assembly.Activity;
import com.alibaba.smart.framework.engine.model.instance.TaskAssigneeCandidateInstance;
import com.winit.workflow.engine.core.api.WorkflowTaskAssigneeDispatcher;
import com.winit.workflow.engine.core.api.model.WorkflowTaskAssigneeCandidateInstance;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * SmartEngineTaskAssigneeDispatcher的单元测试
 */
public class SmartEngineTaskAssigneeDispatcherTest {

    @Mock
    private InstanceAccessor mockInstanceAccessor;

    @Mock
    private Activity mockActivity;

    @Mock
    private ExecutionContext mockExecutionContext;

    @Mock
    private WorkflowTaskAssigneeDispatcher mockWorkflowDispatcher;

    private SmartEngineTaskAssigneeDispatcher dispatcher;
    private Map<String, Object> requestMap;
    private Map<String, Object> responseMap;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        // 准备测试数据
        requestMap = new HashMap<>();
        requestMap.put("userId", "user123");
        requestMap.put("taskType", "approval");

        responseMap = new HashMap<>();

        // 配置Mock对象
        when(mockExecutionContext.getRequest()).thenReturn(requestMap);
        when(mockExecutionContext.getResponse()).thenReturn(responseMap);
        when(mockActivity.getId()).thenReturn("testActivity");

        // 创建调度器
        dispatcher = new SmartEngineTaskAssigneeDispatcher(mockInstanceAccessor);
    }

    @Test
    public void testConstructorWithValidInstanceAccessor() {
        // 验证构造函数正常工作
        SmartEngineTaskAssigneeDispatcher testDispatcher = 
            new SmartEngineTaskAssigneeDispatcher(mockInstanceAccessor);
        assertNotNull(testDispatcher);
    }

    @Test
    public void testGetTaskAssigneeCandidateInstanceWithValidDispatcher() {
        // 准备测试数据
        List<WorkflowTaskAssigneeCandidateInstance> workflowCandidates = new ArrayList<>();
        WorkflowTaskAssigneeCandidateInstance candidate = new WorkflowTaskAssigneeCandidateInstance();
        candidate.setAssigneeId("user123");
        candidate.setAssigneeType("USER");
        workflowCandidates.add(candidate);

        // 配置Mock对象
        when(mockInstanceAccessor.access("workflowTaskAssigneeDispatcher"))
            .thenReturn(mockWorkflowDispatcher);
        when(mockWorkflowDispatcher.dispatch(any(Map.class)))
            .thenReturn(workflowCandidates);

        // 执行测试
        List<TaskAssigneeCandidateInstance> result = 
            dispatcher.getTaskAssigneeCandidateInstance(mockActivity, mockExecutionContext);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        
        // 验证Mock对象被正确调用
        verify(mockInstanceAccessor, times(1)).access("workflowTaskAssigneeDispatcher");
        verify(mockWorkflowDispatcher, times(1)).dispatch(any(Map.class));
    }

    @Test
    public void testGetTaskAssigneeCandidateInstanceWithClassNameAccess() {
        // 配置Mock对象 - 第一次access返回null，第二次返回dispatcher
        when(mockInstanceAccessor.access("workflowTaskAssigneeDispatcher"))
            .thenReturn(null);
        when(mockInstanceAccessor.access(WorkflowTaskAssigneeDispatcher.class.getName()))
            .thenReturn(mockWorkflowDispatcher);

        List<WorkflowTaskAssigneeCandidateInstance> workflowCandidates = new ArrayList<>();
        when(mockWorkflowDispatcher.dispatch(any(Map.class)))
            .thenReturn(workflowCandidates);

        // 执行测试
        List<TaskAssigneeCandidateInstance> result = 
            dispatcher.getTaskAssigneeCandidateInstance(mockActivity, mockExecutionContext);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.size());

        // 验证Mock对象被正确调用
        verify(mockInstanceAccessor, times(1)).access("workflowTaskAssigneeDispatcher");
        verify(mockInstanceAccessor, times(1)).access(WorkflowTaskAssigneeDispatcher.class.getName());
    }

    @Test(expected = RuntimeException.class)
    public void testGetTaskAssigneeCandidateInstanceWithNullInstanceAccessor() {
        // 创建没有InstanceAccessor的调度器
        SmartEngineTaskAssigneeDispatcher nullDispatcher = 
            new SmartEngineTaskAssigneeDispatcher(null);

        // 执行测试 - 应该抛出异常
        nullDispatcher.getTaskAssigneeCandidateInstance(mockActivity, mockExecutionContext);
    }

    @Test(expected = RuntimeException.class)
    public void testGetTaskAssigneeCandidateInstanceWithNoDispatcherFound() {
        // 配置Mock对象 - 所有access都返回null
        when(mockInstanceAccessor.access(anyString())).thenReturn(null);

        // 执行测试 - 应该抛出异常
        dispatcher.getTaskAssigneeCandidateInstance(mockActivity, mockExecutionContext);
    }

    @Test(expected = RuntimeException.class)
    public void testGetTaskAssigneeCandidateInstanceWithInvalidDispatcher() {
        // 配置Mock对象 - 返回非WorkflowTaskAssigneeDispatcher类型的对象
        when(mockInstanceAccessor.access("workflowTaskAssigneeDispatcher"))
            .thenReturn("invalid_dispatcher");

        // 执行测试 - 应该抛出异常
        dispatcher.getTaskAssigneeCandidateInstance(mockActivity, mockExecutionContext);
    }

    @Test
    public void testGetTaskAssigneeCandidateInstanceWithEmptyResult() {
        // 准备测试数据 - 空的候选人列表
        List<WorkflowTaskAssigneeCandidateInstance> emptyList = new ArrayList<>();

        // 配置Mock对象
        when(mockInstanceAccessor.access("workflowTaskAssigneeDispatcher"))
            .thenReturn(mockWorkflowDispatcher);
        when(mockWorkflowDispatcher.dispatch(any(Map.class)))
            .thenReturn(emptyList);

        // 执行测试
        List<TaskAssigneeCandidateInstance> result = 
            dispatcher.getTaskAssigneeCandidateInstance(mockActivity, mockExecutionContext);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetTaskAssigneeCandidateInstanceWithMultipleCandidates() {
        // 准备测试数据 - 多个候选人
        List<WorkflowTaskAssigneeCandidateInstance> workflowCandidates = new ArrayList<>();
        
        WorkflowTaskAssigneeCandidateInstance candidate1 = new WorkflowTaskAssigneeCandidateInstance();
        candidate1.setAssigneeId("user1");
        candidate1.setAssigneeType("USER");
        workflowCandidates.add(candidate1);

        WorkflowTaskAssigneeCandidateInstance candidate2 = new WorkflowTaskAssigneeCandidateInstance();
        candidate2.setAssigneeId("role1");
        candidate2.setAssigneeType("ROLE");
        workflowCandidates.add(candidate2);

        // 配置Mock对象
        when(mockInstanceAccessor.access("workflowTaskAssigneeDispatcher"))
            .thenReturn(mockWorkflowDispatcher);
        when(mockWorkflowDispatcher.dispatch(any(Map.class)))
            .thenReturn(workflowCandidates);

        // 执行测试
        List<TaskAssigneeCandidateInstance> result = 
            dispatcher.getTaskAssigneeCandidateInstance(mockActivity, mockExecutionContext);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
    }

    @Test(expected = RuntimeException.class)
    public void testGetTaskAssigneeCandidateInstanceWithDispatcherException() {
        // 配置Mock对象 - dispatcher抛出异常
        when(mockInstanceAccessor.access("workflowTaskAssigneeDispatcher"))
            .thenReturn(mockWorkflowDispatcher);
        when(mockWorkflowDispatcher.dispatch(any(Map.class)))
            .thenThrow(new RuntimeException("Dispatcher error"));

        // 执行测试 - 应该抛出异常
        dispatcher.getTaskAssigneeCandidateInstance(mockActivity, mockExecutionContext);
    }

    @Test
    public void testGetTaskAssigneeCandidateInstanceWithInstanceAccessorException() {
        // 配置Mock对象 - instanceAccessor抛出异常，但应该有fallback
        when(mockInstanceAccessor.access(anyString()))
            .thenThrow(new RuntimeException("Access error"));

        // 执行测试 - 应该尝试反射fallback，最终抛出异常
        try {
            dispatcher.getTaskAssigneeCandidateInstance(mockActivity, mockExecutionContext);
            fail("Expected RuntimeException");
        } catch (RuntimeException e) {
            assertTrue(e.getMessage().contains("Failed to initialize"));
        }
    }

    @Test
    public void testBuildInputParameterContainsActivityAndContext() {
        // 配置Mock对象
        when(mockInstanceAccessor.access("workflowTaskAssigneeDispatcher"))
            .thenReturn(mockWorkflowDispatcher);
        when(mockWorkflowDispatcher.dispatch(any(Map.class)))
            .thenReturn(new ArrayList<>());

        // 执行测试
        dispatcher.getTaskAssigneeCandidateInstance(mockActivity, mockExecutionContext);

        // 验证dispatch方法被调用，参数包含activity和context信息
        verify(mockWorkflowDispatcher, times(1)).dispatch(argThat(map -> {
            // 验证传递给dispatch的Map包含预期的数据
            return map != null && map.containsKey("activity") && map.containsKey("context");
        }));
    }

    @Test
    public void testLazyInitialization() {
        // 配置Mock对象
        when(mockInstanceAccessor.access("workflowTaskAssigneeDispatcher"))
            .thenReturn(mockWorkflowDispatcher);
        when(mockWorkflowDispatcher.dispatch(any(Map.class)))
            .thenReturn(new ArrayList<>());

        // 第一次调用
        dispatcher.getTaskAssigneeCandidateInstance(mockActivity, mockExecutionContext);

        // 第二次调用
        dispatcher.getTaskAssigneeCandidateInstance(mockActivity, mockExecutionContext);

        // 验证instanceAccessor只被调用一次（延迟初始化）
        verify(mockInstanceAccessor, times(1)).access("workflowTaskAssigneeDispatcher");

        // 但dispatch被调用两次
        verify(mockWorkflowDispatcher, times(2)).dispatch(any(Map.class));
    }

    @Test
    public void testThreadSafetyOfInitialization() throws InterruptedException {
        // 配置Mock对象
        when(mockInstanceAccessor.access("workflowTaskAssigneeDispatcher"))
            .thenReturn(mockWorkflowDispatcher);
        when(mockWorkflowDispatcher.dispatch(any(Map.class)))
            .thenReturn(new ArrayList<>());

        // 创建多个线程同时调用
        Thread[] threads = new Thread[10];
        for (int i = 0; i < 10; i++) {
            threads[i] = new Thread(() -> {
                try {
                    dispatcher.getTaskAssigneeCandidateInstance(mockActivity, mockExecutionContext);
                } catch (Exception e) {
                    // 忽略异常，主要测试线程安全性
                }
            });
        }

        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }

        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }

        // 验证instanceAccessor只被调用一次（线程安全的延迟初始化）
        verify(mockInstanceAccessor, times(1)).access("workflowTaskAssigneeDispatcher");
    }
}
