package com.winit.workflow.engine.core.spi;

import com.winit.workflow.engine.core.provider.defaultengine.DefaultEngineProvider;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * SpiProviderFactory的单元测试
 */
public class SpiProviderFactoryTest {

    @Test
    public void testGetProvider() {
        // 获取提供者
        WorkflowEngineProvider provider = SpiProviderFactory.getProvider();

        // 验证提供者不为null
        assertNotNull(provider);
        
        // 验证提供者类型
        assertNotNull(provider.getType());
        assertNotNull(provider.getDescription());
    }

    @Test
    public void testGetProviderConsistency() {
        // 多次调用应该返回相同类型的提供者
        WorkflowEngineProvider provider1 = SpiProviderFactory.getProvider();
        WorkflowEngineProvider provider2 = SpiProviderFactory.getProvider();

        assertNotNull(provider1);
        assertNotNull(provider2);
        assertEquals(provider1.getType(), provider2.getType());
    }

    @Test
    public void testProviderHasValidType() {
        // 获取提供者
        WorkflowEngineProvider provider = SpiProviderFactory.getProvider();

        // 验证类型不为空且有意义
        String type = provider.getType();
        assertNotNull(type);
        assertFalse(type.trim().isEmpty());
    }

    @Test
    public void testProviderHasValidDescription() {
        // 获取提供者
        WorkflowEngineProvider provider = SpiProviderFactory.getProvider();

        // 验证描述不为空且有意义
        String description = provider.getDescription();
        assertNotNull(description);
        assertFalse(description.trim().isEmpty());
    }

    @Test
    public void testDefaultProviderFallback() {
        // 在没有其他实现的情况下，应该返回DefaultEngineProvider
        WorkflowEngineProvider provider = SpiProviderFactory.getProvider();
        
        // 验证提供者
        assertNotNull(provider);
        
        // 如果是默认提供者，验证其特性
        if (provider instanceof DefaultEngineProvider) {
            assertEquals("default", provider.getType());
            assertTrue(provider.getDescription().contains("fallback"));
        }
    }

    @Test
    public void testProviderCreateEngineThrowsException() {
        // 获取提供者
        WorkflowEngineProvider provider = SpiProviderFactory.getProvider();
        
        // 如果是默认提供者，创建引擎应该抛出异常
        if (provider instanceof DefaultEngineProvider) {
            try {
                provider.createEngine(null);
                fail("Expected UnsupportedOperationException");
            } catch (UnsupportedOperationException e) {
                assertTrue(e.getMessage().contains("DefaultEngineProvider"));
                assertTrue(e.getMessage().contains("fallback"));
            }
        }
    }

    @Test
    public void testProviderTypeNotNull() {
        // 获取提供者
        WorkflowEngineProvider provider = SpiProviderFactory.getProvider();
        
        // 验证类型方法不返回null
        String type = provider.getType();
        assertNotNull("Provider type should not be null", type);
    }

    @Test
    public void testProviderDescriptionNotNull() {
        // 获取提供者
        WorkflowEngineProvider provider = SpiProviderFactory.getProvider();
        
        // 验证描述方法不返回null
        String description = provider.getDescription();
        assertNotNull("Provider description should not be null", description);
    }

    @Test
    public void testMultipleCallsReturnSameProviderType() {
        // 多次调用获取提供者
        WorkflowEngineProvider provider1 = SpiProviderFactory.getProvider();
        WorkflowEngineProvider provider2 = SpiProviderFactory.getProvider();
        WorkflowEngineProvider provider3 = SpiProviderFactory.getProvider();

        // 验证所有提供者类型相同
        assertEquals(provider1.getType(), provider2.getType());
        assertEquals(provider2.getType(), provider3.getType());
    }
}
