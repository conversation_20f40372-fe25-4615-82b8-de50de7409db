package com.winit.workflow.engine.core.common;

import org.junit.Before;
import org.junit.Test;

import java.lang.reflect.Type;

import static org.junit.Assert.*;

/**
 * GenericTypeFetcher的单元测试
 */
public class GenericTypeFetcherTest {

    private TestGenericTypeFetcher<String> stringFetcher;
    private TestGenericTypeFetcher<Integer> integerFetcher;

    @Before
    public void setUp() {
        stringFetcher = new TestGenericTypeFetcher<String>() {};
        integerFetcher = new TestGenericTypeFetcher<Integer>() {};
    }

    @Test
    public void testGetTypeWithString() {
        // 获取类型
        Type type = stringFetcher.getType();

        // 验证类型
        assertNotNull(type);
        assertEquals(String.class, type);
    }

    @Test
    public void testGetTypeWithInteger() {
        // 获取类型
        Type type = integerFetcher.getType();

        // 验证类型
        assertNotNull(type);
        assertEquals(Integer.class, type);
    }

    @Test
    public void testGetTypeWithComplexType() {
        // 创建复杂类型的fetcher
        TestGenericTypeFetcher<java.util.List<String>> listFetcher = new TestGenericTypeFetcher<java.util.List<String>>() {};

        // 获取类型
        Type type = listFetcher.getType();

        // 验证类型
        assertNotNull(type);
        assertTrue(type.toString().contains("java.util.List"));
    }

    @Test
    public void testGetTypeConsistency() {
        // 多次调用应该返回相同的类型
        Type type1 = stringFetcher.getType();
        Type type2 = stringFetcher.getType();

        assertEquals(type1, type2);
        assertSame(type1, type2);
    }

    @Test
    public void testDifferentInstancesSameType() {
        // 创建另一个String类型的fetcher
        TestGenericTypeFetcher<String> anotherStringFetcher = new TestGenericTypeFetcher<String>() {};

        // 获取类型
        Type type1 = stringFetcher.getType();
        Type type2 = anotherStringFetcher.getType();

        // 验证类型相同
        assertEquals(type1, type2);
    }

    @Test
    public void testConcreteImplementation() {
        // 创建具体实现类
        ConcreteStringFetcher concreteFetcher = new ConcreteStringFetcher();

        // 获取类型
        Type type = concreteFetcher.getType();

        // 验证类型
        assertNotNull(type);
        assertEquals(String.class, type);
    }

    @Test
    public void testNestedGenericType() {
        // 创建嵌套泛型类型的fetcher
        TestGenericTypeFetcher<java.util.Map<String, Integer>> mapFetcher = 
            new TestGenericTypeFetcher<java.util.Map<String, Integer>>() {};

        // 获取类型
        Type type = mapFetcher.getType();

        // 验证类型
        assertNotNull(type);
        assertTrue(type.toString().contains("java.util.Map"));
    }

    /**
     * 测试用的GenericTypeFetcher实现
     */
    public abstract static class TestGenericTypeFetcher<T> extends GenericTypeFetcher<T> {
        // 空实现，用于测试
    }

    /**
     * 具体的String类型实现
     */
    public static class ConcreteStringFetcher extends GenericTypeFetcher<String> {
        // 具体实现类
    }
}
