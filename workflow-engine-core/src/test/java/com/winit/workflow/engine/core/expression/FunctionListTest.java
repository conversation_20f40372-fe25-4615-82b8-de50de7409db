package com.winit.workflow.engine.core.expression;

import com.winit.workflow.engine.core.expression.provider.MvelExpressionEngine;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

import static org.junit.Assert.*;

/**
 * 测试表达式函数列表功能
 */
public class FunctionListTest {
    
    private ExpressionService expressionService;
    
    @Before
    public void setUp() {
        // 创建表达式引擎和服务
        ExpressionEngine engine = new MvelExpressionEngine();
        expressionService = new DefaultExpressionService(engine);
    }
    
    @Test
    public void testGetAllFunctions() {
        // 获取所有函数
        List<FunctionInfo> functions = expressionService.getAllFunctions();

        // 验证函数列表不为空
        assertNotNull("Functions list should not be null", functions);
        assertEquals("Should have 14 functions (4 builtin + 10 custom)", 14, functions.size());

        // 打印所有函数信息
        System.out.println("=== All Available Expression Functions ===");
        System.out.println("Total functions: " + functions.size());
        System.out.println();

        // 分类统计
        int builtinCount = 0;
        int customCount = 0;

        for (FunctionInfo function : functions) {
            if ("builtin".equals(function.getType())) {
                builtinCount++;
            } else if ("custom".equals(function.getType())) {
                customCount++;
            }
            printFunctionInfo(function);
        }

        System.out.println("=== Function Summary ===");
        System.out.println("Builtin functions: " + builtinCount);
        System.out.println("Custom functions: " + customCount);
        System.out.println("Total functions: " + functions.size());

        // 验证函数数量
        assertEquals("Should have 4 builtin functions", 4, builtinCount);
        assertEquals("Should have 10 custom functions", 10, customCount);

        // 验证内置函数
        verifyBuiltinFunctions(functions);

        // 验证自定义函数
        verifyCustomFunctions(functions);
    }
    
    @Test
    public void testFunctionInfoContent() {
        List<FunctionInfo> functions = expressionService.getAllFunctions();

        for (FunctionInfo function : functions) {
            // 验证基本信息不为空
            assertNotNull("Function name should not be null", function.getFunctionName());
            assertNotNull("Function description should not be null", function.getDescription());
            assertNotNull("Function example should not be null", function.getExample());
            assertNotNull("Function signature should not be null", function.getFunctionSignature());
            assertNotNull("Function type should not be null", function.getType());

            // 验证函数名不为空
            assertFalse("Function name should not be empty", function.getFunctionName().trim().isEmpty());

            // 验证优先级合理
            assertTrue("Priority should be positive", function.getPriority() > 0);

            // 验证参数数量合理
            assertTrue("Parameter count should be >= -1", function.getParameterCount() >= -1);

            // 验证函数类型
            assertTrue("Function type should be builtin or custom",
                "builtin".equals(function.getType()) || "custom".equals(function.getType()));
        }
    }

    @Test
    public void testCustomFunctionExecution() {
        System.out.println("\n=== Testing Custom Function Execution ===");

        // Test some custom functions to ensure they work correctly
        testAbsFunction();
        testMaxMinFunctions();
        testStringFunctions();
        testConversionFunctions();

        System.out.println("All custom function tests passed!");
    }

    private void testAbsFunction() {
        // This would require actual expression evaluation, which is beyond the scope of function listing
        // But we can verify the function is properly registered
        List<FunctionInfo> functions = expressionService.getAllFunctions();
        FunctionInfo absFunction = findFunction(functions, "abs");
        assertNotNull("abs function should be registered", absFunction);
        assertEquals("abs should have 1 parameter", 1, absFunction.getParameterCount());
        assertTrue("abs description should mention absolute value",
            absFunction.getDescription().toLowerCase().contains("absolute"));
    }

    private void testMaxMinFunctions() {
        List<FunctionInfo> functions = expressionService.getAllFunctions();

        FunctionInfo maxFunction = findFunction(functions, "max");
        assertNotNull("max function should be registered", maxFunction);
        assertEquals("max should have variable parameters", -1, maxFunction.getParameterCount());

        FunctionInfo minFunction = findFunction(functions, "min");
        assertNotNull("min function should be registered", minFunction);
        assertEquals("min should have variable parameters", -1, minFunction.getParameterCount());
    }

    private void testStringFunctions() {
        List<FunctionInfo> functions = expressionService.getAllFunctions();

        FunctionInfo containsFunction = findFunction(functions, "contains");
        assertNotNull("contains function should be registered", containsFunction);
        assertEquals("contains should have 2 parameters", 2, containsFunction.getParameterCount());

        FunctionInfo substringFunction = findFunction(functions, "substring");
        assertNotNull("substring function should be registered", substringFunction);
        assertEquals("substring should have variable parameters", -1, substringFunction.getParameterCount());
    }

    private void testConversionFunctions() {
        List<FunctionInfo> functions = expressionService.getAllFunctions();

        FunctionInfo toIntFunction = findFunction(functions, "toInt");
        assertNotNull("toInt function should be registered", toIntFunction);
        assertEquals("toInt should have 1 parameter", 1, toIntFunction.getParameterCount());

        FunctionInfo toStringFunction = findFunction(functions, "toString");
        assertNotNull("toString function should be registered", toStringFunction);
        assertEquals("toString should have variable parameters", -1, toStringFunction.getParameterCount());
    }
    
    private void verifyBuiltinFunctions(List<FunctionInfo> functions) {
        String[] expectedBuiltins = {"concat", "uppercase", "lowercase", "now"};

        for (String expectedName : expectedBuiltins) {
            FunctionInfo function = findFunction(functions, expectedName);
            assertNotNull("Builtin function should be found: " + expectedName, function);
            assertEquals("Should be builtin type", "builtin", function.getType());
            assertEquals("Builtin functions should have priority 10", 10, function.getPriority());

            // Verify specific builtin function properties
            switch (expectedName) {
                case "concat":
                    assertEquals("concat should have variable parameters", -1, function.getParameterCount());
                    break;
                case "uppercase":
                case "lowercase":
                    assertEquals(expectedName + " should have 1 parameter", 1, function.getParameterCount());
                    break;
                case "now":
                    assertEquals("now should have 0 parameters", 0, function.getParameterCount());
                    break;
            }
        }
    }

    private void verifyCustomFunctions(List<FunctionInfo> functions) {
        String[] expectedCustoms = {"abs", "compareDate", "convertLength", "toInt", "toString",
                                   "max", "min", "round", "substring", "contains"};

        for (String expectedName : expectedCustoms) {
            FunctionInfo function = findFunction(functions, expectedName);
            assertNotNull("Custom function should be found: " + expectedName, function);
            assertEquals("Should be custom type", "custom", function.getType());
            assertEquals("Custom functions should have priority 200", 200, function.getPriority());

            // Verify specific custom function properties
            switch (expectedName) {
                case "abs":
                case "toInt":
                case "compareDate":
                case "contains":
                    assertEquals(expectedName + " should have 1-2 parameters",
                        true, function.getParameterCount() >= 1 && function.getParameterCount() <= 2);
                    break;
                case "convertLength":
                    assertEquals("convertLength should have 3 parameters", 3, function.getParameterCount());
                    break;
                case "toString":
                case "max":
                case "min":
                case "round":
                case "substring":
                    assertEquals(expectedName + " should have variable parameters", -1, function.getParameterCount());
                    break;
            }
        }
    }

    private FunctionInfo findFunction(List<FunctionInfo> functions, String name) {
        for (FunctionInfo function : functions) {
            if (name.equals(function.getFunctionName())) {
                return function;
            }
        }
        return null;
    }

    private void printFunctionInfo(FunctionInfo function) {
        System.out.println("Function: " + function.getFunctionName());
        System.out.println("Type: " + function.getType());
        System.out.println("Description: " + function.getDescription());
        System.out.println("Parameters: " + function.getParameterCount() +
            (function.getParameterCount() == -1 ? " (variable)" : ""));
        System.out.println("Priority: " + function.getPriority());
        System.out.println("Signature:");
        System.out.println(function.getFunctionSignature());
        System.out.println("Examples:");
        System.out.println(function.getExample());
        System.out.println("----------------------------------------");
    }
}
