package com.winit.workflow.engine.core.executor.service;

import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * ServiceExecutor的单元测试
 */
public class ServiceExecutorTest {

    private TestServiceExecutor testExecutor;

    @Before
    public void setUp() {
        testExecutor = new TestServiceExecutor();
    }

    @Test
    public void testGetInType() {
        // 验证输入类型获取
        Class<?> inType = testExecutor.getInType();
        assertEquals(TestRequest.class, inType);
    }

    @Test
    public void testGetOutType() {
        // 验证输出类型获取
        Class<?> outType = testExecutor.getOutType();
        assertEquals(TestResponse.class, outType);
    }

    @Test
    public void testExecute() {
        // 准备测试数据
        TestRequest request = new TestRequest();
        request.setMessage("test message");
        request.setNumber(42);

        // 执行
        TestResponse response = testExecutor.execute(request);

        // 验证结果
        assertNotNull(response);
        assertEquals("Processed: test message", response.getResult());
        assertEquals(Integer.valueOf(42), response.getCode());
    }

    @Test
    public void testExecuteWithNullInput() {
        // 执行
        TestResponse response = testExecutor.execute(null);

        // 验证结果
        assertNotNull(response);
        assertEquals("Processed: null", response.getResult());
        assertNull(response.getCode());
    }

    @Test
    public void testExecuteWithEmptyMessage() {
        // 准备测试数据
        TestRequest request = new TestRequest();
        request.setMessage("");
        request.setNumber(0);

        // 执行
        TestResponse response = testExecutor.execute(request);

        // 验证结果
        assertNotNull(response);
        assertEquals("Processed: ", response.getResult());
        assertEquals(Integer.valueOf(0), response.getCode());
    }

    /**
     * 测试用的ServiceExecutor实现
     */
    public static class TestServiceExecutor extends ServiceExecutor<TestRequest, TestResponse> {

        @Override
        public TestResponse execute(TestRequest request) {
            TestResponse response = new TestResponse();
            if (request != null) {
                response.setResult("Processed: " + request.getMessage());
                response.setCode(request.getNumber());
            } else {
                response.setResult("Processed: null");
                response.setCode(null);
            }
            return response;
        }
    }

    /**
     * 测试请求类
     */
    public static class TestRequest extends ServiceExecutorRequest<TestRequest> {
        private String message;
        private Integer number;

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public Integer getNumber() {
            return number;
        }

        public void setNumber(Integer number) {
            this.number = number;
        }
    }

    /**
     * 测试响应类
     */
    public static class TestResponse extends ServiceExecutorResponse<TestResponse> {
        private String result;
        private Integer code;

        public String getResult() {
            return result;
        }

        public void setResult(String result) {
            this.result = result;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }
    }
}
