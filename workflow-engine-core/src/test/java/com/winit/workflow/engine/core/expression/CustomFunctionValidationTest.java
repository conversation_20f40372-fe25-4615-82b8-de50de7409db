package com.winit.workflow.engine.core.expression;

import com.winit.workflow.engine.core.expression.function.custom.*;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * 自定义表达式函数参数验证测试
 * 验证每个函数的 validateParameters 方法正确工作
 */
public class CustomFunctionValidationTest {
    
    // ==================== abs 函数参数验证测试 ====================
    
    @Test
    public void testAbsFunctionValidation() {
        AbsFunction function = new AbsFunction();
        
        // 正确的参数数量
        assertTrue("Should accept 1 parameter", function.validateParameters(5));
        assertTrue("Should accept 1 parameter", function.validateParameters(-3.14));
        
        // 错误的参数数量
        assertFalse("Should reject 0 parameters", function.validateParameters());
        assertFalse("Should reject 2 parameters", function.validateParameters(1, 2));
        assertFalse("Should reject null parameters", function.validateParameters((Object[]) null));
    }
    
    // ==================== compareDate 函数参数验证测试 ====================
    
    @Test
    public void testCompareDateFunctionValidation() {
        CompareDateFunction function = new CompareDateFunction();
        
        // 正确的参数数量
        assertTrue("Should accept 2 parameters", function.validateParameters("2025-01-01", "2025-01-02"));
        
        // 错误的参数数量
        assertFalse("Should reject 0 parameters", function.validateParameters());
        assertFalse("Should reject 1 parameter", function.validateParameters("2025-01-01"));
        assertFalse("Should reject 3 parameters", function.validateParameters("2025-01-01", "2025-01-02", "2025-01-03"));
        assertFalse("Should reject null parameters", function.validateParameters((Object[]) null));
    }
    
    // ==================== convertLength 函数参数验证测试 ====================
    
    @Test
    public void testConvertLengthFunctionValidation() {
        ConvertLengthFunction function = new ConvertLengthFunction();
        
        // 正确的参数数量
        assertTrue("Should accept 3 parameters", function.validateParameters(1, "m", "ft"));
        assertTrue("Should accept 3 parameters", function.validateParameters(100, "cm", "in"));
        
        // 错误的参数数量
        assertFalse("Should reject 0 parameters", function.validateParameters());
        assertFalse("Should reject 1 parameter", function.validateParameters(1));
        assertFalse("Should reject 2 parameters", function.validateParameters(1, "m"));
        assertFalse("Should reject 4 parameters", function.validateParameters(1, "m", "ft", "extra"));
        assertFalse("Should reject null parameters", function.validateParameters((Object[]) null));
    }
    
    // ==================== toInt 函数参数验证测试 ====================
    
    @Test
    public void testToIntFunctionValidation() {
        ToIntFunction function = new ToIntFunction();
        
        // 正确的参数数量
        assertTrue("Should accept 1 parameter", function.validateParameters("123"));
        assertTrue("Should accept 1 parameter", function.validateParameters(3.14));
        
        // 错误的参数数量
        assertFalse("Should reject 0 parameters", function.validateParameters());
        assertFalse("Should reject 2 parameters", function.validateParameters("123", "456"));
        assertFalse("Should reject null parameters", function.validateParameters((Object[]) null));
    }
    
    // ==================== toString 函数参数验证测试 ====================
    
    @Test
    public void testToStringFunctionValidation() {
        ToStringFunction function = new ToStringFunction();
        
        // 正确的参数数量（1或2个参数）
        assertTrue("Should accept 1 parameter", function.validateParameters(123));
        assertTrue("Should accept 2 parameters", function.validateParameters(123.456, "#.##"));
        
        // 错误的参数数量
        assertFalse("Should reject 0 parameters", function.validateParameters());
        assertFalse("Should reject 3 parameters", function.validateParameters(123, "#.##", "extra"));
        assertFalse("Should reject null parameters", function.validateParameters((Object[]) null));
    }
    
    // ==================== max 函数参数验证测试 ====================
    
    @Test
    public void testMaxFunctionValidation() {
        MaxFunction function = new MaxFunction();
        
        // 正确的参数数量（至少1个）
        assertTrue("Should accept 1 parameter", function.validateParameters(5));
        assertTrue("Should accept 2 parameters", function.validateParameters(1, 2));
        assertTrue("Should accept multiple parameters", function.validateParameters(1, 2, 3, 4, 5));
        
        // 错误的参数数量
        assertFalse("Should reject 0 parameters", function.validateParameters());
        assertFalse("Should reject null parameters", function.validateParameters((Object[]) null));
    }
    
    // ==================== min 函数参数验证测试 ====================
    
    @Test
    public void testMinFunctionValidation() {
        MinFunction function = new MinFunction();
        
        // 正确的参数数量（至少1个）
        assertTrue("Should accept 1 parameter", function.validateParameters(5));
        assertTrue("Should accept 2 parameters", function.validateParameters(1, 2));
        assertTrue("Should accept multiple parameters", function.validateParameters(1, 2, 3, 4, 5));
        
        // 错误的参数数量
        assertFalse("Should reject 0 parameters", function.validateParameters());
        assertFalse("Should reject null parameters", function.validateParameters((Object[]) null));
    }
    
    // ==================== round 函数参数验证测试 ====================
    
    @Test
    public void testRoundFunctionValidation() {
        RoundFunction function = new RoundFunction();
        
        // 正确的参数数量（1或2个参数）
        assertTrue("Should accept 1 parameter", function.validateParameters(3.14));
        assertTrue("Should accept 2 parameters", function.validateParameters(3.14159, 2));
        
        // 错误的参数数量
        assertFalse("Should reject 0 parameters", function.validateParameters());
        assertFalse("Should reject 3 parameters", function.validateParameters(3.14, 2, "extra"));
        assertFalse("Should reject null parameters", function.validateParameters((Object[]) null));
    }
    
    // ==================== substring 函数参数验证测试 ====================
    
    @Test
    public void testSubstringFunctionValidation() {
        SubstringFunction function = new SubstringFunction();
        
        // 正确的参数数量（2或3个参数）
        assertTrue("Should accept 2 parameters", function.validateParameters("Hello", 0));
        assertTrue("Should accept 3 parameters", function.validateParameters("Hello", 0, 5));
        
        // 错误的参数数量
        assertFalse("Should reject 0 parameters", function.validateParameters());
        assertFalse("Should reject 1 parameter", function.validateParameters("Hello"));
        assertFalse("Should reject 4 parameters", function.validateParameters("Hello", 0, 5, "extra"));
        assertFalse("Should reject null parameters", function.validateParameters((Object[]) null));
    }
    
    // ==================== contains 函数参数验证测试 ====================
    
    @Test
    public void testContainsFunctionValidation() {
        ContainsFunction function = new ContainsFunction();
        
        // 正确的参数数量
        assertTrue("Should accept 2 parameters", function.validateParameters("Hello World", "World"));
        assertTrue("Should accept 2 parameters", function.validateParameters("text", "search"));
        
        // 错误的参数数量
        assertFalse("Should reject 0 parameters", function.validateParameters());
        assertFalse("Should reject 1 parameter", function.validateParameters("Hello"));
        assertFalse("Should reject 3 parameters", function.validateParameters("Hello", "World", "extra"));
        assertFalse("Should reject null parameters", function.validateParameters((Object[]) null));
    }
    
    // ==================== 函数基本信息验证测试 ====================
    
    @Test
    public void testFunctionBasicInfo() {
        // 验证所有函数的基本信息
        AbsFunction abs = new AbsFunction();
        assertEquals("abs", abs.getFunctionName());
        assertEquals(1, abs.getParameterCount());
        assertEquals(200, abs.getPriority());
        assertNotNull(abs.getDescription());
        assertNotNull(abs.getExample());
        assertNotNull(abs.getFunctionSignature());
        
        CompareDateFunction compareDate = new CompareDateFunction();
        assertEquals("compareDate", compareDate.getFunctionName());
        assertEquals(2, compareDate.getParameterCount());
        assertEquals(200, compareDate.getPriority());
        
        ConvertLengthFunction convertLength = new ConvertLengthFunction();
        assertEquals("convertLength", convertLength.getFunctionName());
        assertEquals(3, convertLength.getParameterCount());
        assertEquals(200, convertLength.getPriority());
        
        ToIntFunction toInt = new ToIntFunction();
        assertEquals("toInt", toInt.getFunctionName());
        assertEquals(1, toInt.getParameterCount());
        assertEquals(200, toInt.getPriority());
        
        ToStringFunction toString = new ToStringFunction();
        assertEquals("toString", toString.getFunctionName());
        assertEquals(-1, toString.getParameterCount()); // Variable parameters
        assertEquals(200, toString.getPriority());
        
        MaxFunction max = new MaxFunction();
        assertEquals("max", max.getFunctionName());
        assertEquals(-1, max.getParameterCount()); // Variable parameters
        assertEquals(200, max.getPriority());
        
        MinFunction min = new MinFunction();
        assertEquals("min", min.getFunctionName());
        assertEquals(-1, min.getParameterCount()); // Variable parameters
        assertEquals(200, min.getPriority());
        
        RoundFunction round = new RoundFunction();
        assertEquals("round", round.getFunctionName());
        assertEquals(-1, round.getParameterCount()); // Variable parameters
        assertEquals(200, round.getPriority());
        
        SubstringFunction substring = new SubstringFunction();
        assertEquals("substring", substring.getFunctionName());
        assertEquals(-1, substring.getParameterCount()); // Variable parameters
        assertEquals(200, substring.getPriority());
        
        ContainsFunction contains = new ContainsFunction();
        assertEquals("contains", contains.getFunctionName());
        assertEquals(2, contains.getParameterCount());
        assertEquals(200, contains.getPriority());
    }
    
    // ==================== 函数执行基本测试 ====================
    
    @Test
    public void testFunctionDirectExecution() {
        // 直接调用函数的 execute 方法进行基本测试
        
        AbsFunction abs = new AbsFunction();
        assertEquals(5, abs.execute(-5));
        assertEquals(3.14, abs.execute(-3.14));
        
        ToIntFunction toInt = new ToIntFunction();
        assertEquals(123, toInt.execute("123"));
        assertEquals(3, toInt.execute(3.14));
        
        ContainsFunction contains = new ContainsFunction();
        assertEquals(true, contains.execute("Hello World", "World"));
        assertEquals(false, contains.execute("Hello World", "world"));
        
        MaxFunction max = new MaxFunction();
        assertEquals(9, max.execute(1, 5, 3, 9, 2));
        
        MinFunction min = new MinFunction();
        assertEquals(1, min.execute(1, 5, 3, 9, 2));
        
        RoundFunction round = new RoundFunction();
        assertEquals(3.14, round.execute(3.14159, 2));
        assertEquals(3, round.execute(2.7));
    }
}
