<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>


    <groupId>com.winit</groupId>
    <artifactId>workflow-engine</artifactId>
    <version>${workflow-engine.version}</version>


    <packaging>pom</packaging>
    <name>Workflow Engine</name>

    <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <smart-engine.version>3.6.0-SNAPSHOT</smart-engine.version>
        <lombk.version>1.18.38</lombk.version>
        <slf4j.version>1.7.13</slf4j.version>
        <logback.version>1.2.11</logback.version>
        <junit.jupiter.version>5.9.1</junit.jupiter.version>
        <springframework.version>5.3.7</springframework.version>
        <spring-boot.version>2.4.6</spring-boot.version>
        <tomcat.version>9.0.98</tomcat.version>
        <maven_flatten_version>1.5.0</maven_flatten_version>
        <workflow-engine.version>1.0.0-SNAPSHOT</workflow-engine.version>
    </properties>

    <modules>
        <module>workflow-engine-core</module>
        <module>workflow-engine-sdk</module>
        <module>workflow-engine-spring-starter</module>
        <module>workflow-engine-provider</module>
    </modules>

    <dependencyManagement>
        <dependencies>


            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${springframework.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${springframework.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.smart.framework</groupId>
                <artifactId>smart-engine</artifactId>
                <version>${smart-engine.version}</version>
                <type>pom</type>
            </dependency>


            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mvel</groupId>
                <artifactId>mvel2</artifactId>
                <version>2.5.2.Final</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombk.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombk.version}</version>
                        </path>
                    </annotationProcessorPaths>
                    <compilerArgs>
                        <arg>-Xlint:unchecked</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            
            <!-- Checkstyle插件：检查Java代码风格和编码规范 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>3.1.2</version>
                <configuration>
                    <configLocation>checkstyle.xml</configLocation>
                    <encoding>UTF-8</encoding>
                    <consoleOutput>true</consoleOutput>
                    <failsOnError>true</failsOnError>
                    <linkXRef>false</linkXRef>
                    <includeTestSourceDirectory>true</includeTestSourceDirectory>
                </configuration>
                <executions>
                    <execution>
                        <id>validate</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>com.puppycrawl.tools</groupId>
                        <artifactId>checkstyle</artifactId>
                        <version>8.45</version>
                    </dependency>
                </dependencies>
            </plugin>
            
            <!-- PMD代码质量检查 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <version>3.15.0</version>
                <configuration>
                    <sourceEncoding>UTF-8</sourceEncoding>
                    <printFailingErrors>true</printFailingErrors>
                    <includeTests>false</includeTests>
                    <failOnViolation>false</failOnViolation>
                    <targetJdk>1.8</targetJdk>
                    <!-- 只检查重要级别的问题 -->
                    <minimumPriority>2</minimumPriority>
                    <!-- 自定义规则集，排除过于严格的规则 -->
                    <rulesets>
                        <ruleset>/rulesets/java/quickstart.xml</ruleset>
                    </rulesets>
                    <excludeRoots>
                        <excludeRoot>target/generated-sources</excludeRoot>
                    </excludeRoots>
                </configuration>
                <executions>
                    <execution>
                        <id>pmd-check</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${maven_flatten_version}</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>oss</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <phase>process-resources</phase>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                        <phase>clean</phase>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>