package com.winit.workflow.engine.spring.delegation;

import com.winit.workflow.engine.core.executor.service.ServiceExecutorResponse;

public class SimpleServiceTaskResp extends ServiceExecutorResponse<SimpleServiceTaskResp> {

    private String route;

    private String defaultSequenceFlow;

    private String log;
    public String getRoute() {
        return route;
    }

    public void setRoute(String route) {
        this.route = route;
    }

    public String getDefaultSequenceFlow() {
        return defaultSequenceFlow;
    }

    public void setDefaultSequenceFlow(String defaultSequenceFlow) {
        this.defaultSequenceFlow = defaultSequenceFlow;
    }

    public String getLog() {
        return log;
    }

    public void setLog(String log) {
        this.log = log;
    }
}
