package com.winit.workflow.engine.spring.delegation;


import com.winit.workflow.engine.core.executor.service.ServiceExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 简单的业务服务
 */
@Component("simpleServiceTaskDelegation")
public class SimpleServiceTaskDelegation extends ServiceExecutor<SimpleServiceTaskReq, SimpleServiceTaskResp> {

    private static final Logger LOGGER = LoggerFactory.getLogger(SimpleServiceTaskDelegation.class);


    @Override
    public SimpleServiceTaskResp execute(SimpleServiceTaskReq simpleServiceTaskReq) {
        SimpleServiceTaskResp response = new SimpleServiceTaskResp();
        response.setRoute(simpleServiceTaskReq.getRoute());
        response.setDefaultSequenceFlow(simpleServiceTaskReq.getDefaultSequenceFlow());
        response.setLog("SimpleServiceTaskDelegation execute");
        return response;
    }
}