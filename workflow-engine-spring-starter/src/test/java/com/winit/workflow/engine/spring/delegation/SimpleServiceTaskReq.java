package com.winit.workflow.engine.spring.delegation;

import com.winit.workflow.engine.core.executor.service.ServiceExecutorRequest;

public class SimpleServiceTaskReq extends ServiceExecutorRequest<SimpleServiceTaskReq> {

    private String route;

    private String defaultSequenceFlow;

    public String getRoute() {
        return route;
    }

    public void setRoute(String route) {
        this.route = route;
    }

    public String getDefaultSequenceFlow() {
        return defaultSequenceFlow;
    }

    public void setDefaultSequenceFlow(String defaultSequenceFlow) {
        this.defaultSequenceFlow = defaultSequenceFlow;
    }
}
