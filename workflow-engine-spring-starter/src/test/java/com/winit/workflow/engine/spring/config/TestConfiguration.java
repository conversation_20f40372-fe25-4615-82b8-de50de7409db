package com.winit.workflow.engine.spring.config;

import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;

/**
 * Test configuration for Spring Boot tests
 */
@SpringBootConfiguration
@EnableAutoConfiguration
@ComponentScan(basePackages = {
    "com.winit.workflow.engine.spring.controller",
    "com.winit.workflow.engine.spring.config"
})
@Import(ExpressionConfiguration.class)
public class TestConfiguration {
    // This class provides the necessary Spring Boot auto-configuration for tests
}
