package com.winit.workflow.engine.spring;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.context.annotation.Import;
import com.winit.workflow.engine.spring.config.WorkflowSmartEngineConfiguration;

@EnableWorkflowEngine
@SpringBootApplication(exclude = RabbitAutoConfiguration.class)
@Import(WorkflowSmartEngineConfiguration.class)
public class StartUpApplication {
    public static void main(String[] args) {
        SpringApplication.run(StartUpApplication.class, args);
    }
}
