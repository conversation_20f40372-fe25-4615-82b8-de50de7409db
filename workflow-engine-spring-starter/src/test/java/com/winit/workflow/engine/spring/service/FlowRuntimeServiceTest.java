package com.winit.workflow.engine.spring.service;

import com.winit.workflow.engine.core.executor.service.ServiceExecutor;
import com.winit.workflow.engine.core.executor.service.ServiceExecutorRequest;
import com.winit.workflow.engine.core.executor.service.ServiceExecutorResponse;
import com.winit.workflow.engine.sdk.loader.FlowContentLoader;
import com.winit.workflow.engine.sdk.loader.FlowResourceApiLoader;
import com.winit.workflow.engine.sdk.loader.FlowResourcePathLoader;
import com.winit.workflow.engine.sdk.service.FlowRuntimeService;
import com.winit.workflow.engine.sdk.service.impl.FlowRuntimeServiceImpl;
import com.winit.workflow.engine.sdk.vo.request.FlowStartRequest;
import com.winit.workflow.engine.sdk.vo.response.FlowStartResponse;
import com.winit.workflow.engine.spring.StartUpApplication;
import com.winit.workflow.engine.spring.config.WorkflowEngineConfiguration;
import com.winit.workflow.engine.spring.config.WorkflowSmartEngineConfiguration;
import com.winit.workflow.engine.spring.properties.WorkflowManagementProperties;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.when;

@SpringBootTest(classes = StartUpApplication.class)
@Import({WorkflowSmartEngineConfiguration.class, WorkflowEngineConfiguration.class})
public class FlowRuntimeServiceTest {

    @Autowired
    private FlowRuntimeService flowRuntimeService;

    @Autowired
    private WorkflowManagementProperties workflowManagementProperties;

    @MockBean
    private FlowResourceApiLoader flowResourceApiLoader;

    public static class MockRequest extends ServiceExecutorRequest<MockRequest> {
        private String route;
        private String defaultSequenceFlow;
        public String getRoute() { return route; }
        public void setRoute(String route) { this.route = route; }
        public String getDefaultSequenceFlow() { return defaultSequenceFlow; }
        public void setDefaultSequenceFlow(String defaultSequenceFlow) { this.defaultSequenceFlow = defaultSequenceFlow; }
    }
    public static class MockResponse extends ServiceExecutorResponse<MockResponse> {
        private String route;
        private String defaultSequenceFlow;
        public String getRoute() { return route; }
        public void setRoute(String route) { this.route = route; }
        public String getDefaultSequenceFlow() { return defaultSequenceFlow; }
        public void setDefaultSequenceFlow(String defaultSequenceFlow) { this.defaultSequenceFlow = defaultSequenceFlow; }
    }

    @Test
    public void testExecute() {
        // 实现一个Mock ServiceExecutor，避免NPE
        ServiceExecutor<MockRequest, MockResponse> mockExecutor = new ServiceExecutor<MockRequest, MockResponse>() {
            @Override
            public MockResponse execute(MockRequest in) {
                MockResponse resp = new MockResponse();
                resp.setRoute("NoRoute");
                resp.setDefaultSequenceFlow("serviceTask1");
                return resp;
            }
        };


        FlowResourcePathLoader flowResourcePathLoader = new FlowResourcePathLoader("workflow-engine/default-sequence-flow.bpmn.xml");
        FlowStartRequest<FlowContentLoader> flowStartRequest = new FlowStartRequest<FlowContentLoader>();
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("route", "NoRoute");
        parameters.put("defaultSequenceFlow", "serviceTask1");
        flowStartRequest.setVariable(parameters);
        flowStartRequest.setFlowContent(flowResourcePathLoader);
        FlowStartResponse flowStartResponse = flowRuntimeService.execute(flowStartRequest);
        Map<String, Object> variables = flowStartResponse.getResponse();
        Object route = variables.get("route");
        Object processDefinitionActivityId = variables.get("defaultSequenceFlow");
        Assertions.assertEquals("NoRoute", route);
        Assertions.assertEquals("serviceTask1", processDefinitionActivityId);
        System.out.println(flowStartResponse.getResponse().get("log"));
    }


    @Test
    void execute4RemoteLoader() {
        FlowResourcePathLoader fakeLoader = new FlowResourcePathLoader("workflow-engine/default-sequence-flow.bpmn.xml");
        when(flowResourceApiLoader.load()).thenReturn(fakeLoader.load());

        FlowStartRequest<FlowContentLoader> flowStartRequest = new FlowStartRequest<FlowContentLoader>();
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("route", "NoRoute");
        parameters.put("defaultSequenceFlow", "serviceTask1");
        flowStartRequest.setVariable(parameters);
        flowStartRequest.setFlowContent(flowResourceApiLoader);
        FlowStartResponse flowStartResponse = flowRuntimeService.execute(flowStartRequest);
        Map<String, Object> variables = flowStartResponse.getResponse();
        Object route = variables.get("route");
        Object processDefinitionActivityId = variables.get("defaultSequenceFlow");
        Assertions.assertEquals("NoRoute", route);
        Assertions.assertEquals("serviceTask1", processDefinitionActivityId);
        System.out.println(flowStartResponse.getResponse().get("log"));

    }
}
