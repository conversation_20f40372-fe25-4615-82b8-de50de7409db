package com.winit.workflow.engine.spring.manual;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Import;
import org.springframework.context.event.EventListener;
import org.springframework.core.env.Environment;

import com.winit.workflow.engine.spring.config.ExpressionConfiguration;

/**
 * 独立的测试启动类，用于手动测试 ExpressionController REST API
 *
 * 使用方法：
 * 1. 在 IDE 中直接运行此类的 main 方法
 * 2. 或者使用 Maven 命令：mvn exec:java -Dexec.mainClass="com.winit.workflow.engine.spring.manual.ExpressionApiTestApplication"
 *
 * 注意：此类仅用于手动测试，不会被 mvn test 或 mvn package 执行
 * 注意：放在独立的 manual 包中，避免与自动化测试冲突
 */
@SpringBootApplication(scanBasePackages = "com.winit.workflow.engine.spring")
@Import(ExpressionConfiguration.class)
public class ExpressionApiTestApplication {
    
    private static final Logger logger = LoggerFactory.getLogger(ExpressionApiTestApplication.class);
    
    public static void main(String[] args) {
        logger.info(StringUtils.repeat("=", 80));
        logger.info("启动 Expression API 测试服务器...");
        logger.info(StringUtils.repeat("=", 80));
        
        // 设置默认端口为 8080（如果没有指定的话）
        System.setProperty("server.port", System.getProperty("server.port", "8080"));
        
        SpringApplication app = new SpringApplication(ExpressionApiTestApplication.class);
        
        // 设置默认配置
        app.setDefaultProperties(java.util.Collections.singletonMap(
            "spring.main.allow-bean-definition-overriding", "true"
        ));
        
        app.run(args);
    }
    
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady(ApplicationReadyEvent event) {
        Environment env = event.getApplicationContext().getEnvironment();
        String port = env.getProperty("server.port", "8080");
        String contextPath = env.getProperty("server.servlet.context-path", "");
        
        logger.info("");
        logger.info(StringUtils.repeat("=", 80));
        logger.info("🚀 Expression API 测试服务器启动成功！");
        logger.info(StringUtils.repeat("=", 80));
        logger.info("服务器地址: http://localhost:{}{}", port, contextPath);
        logger.info("");
        logger.info("📋 可用的 REST API 端点:");
        logger.info(StringUtils.repeat("=", 80));
        
        String baseUrl = "http://localhost:" + port + contextPath;
        
        // 打印所有可用的 API 端点
        logger.info("1. 获取所有表达式函数:");
        logger.info("   GET {}/api/v1/expression/functions", baseUrl);
        logger.info("   curl {}/api/v1/expression/functions", baseUrl);
        logger.info("");
        
        logger.info("2. 获取指定名称的函数:");
        logger.info("   GET {}/api/v1/expression/functions/{{name}}", baseUrl);
        logger.info("   curl {}/api/v1/expression/functions/calculateTax", baseUrl);
        logger.info("   curl {}/api/v1/expression/functions/generateOrderNumber", baseUrl);
        logger.info("");
        
        logger.info("3. 按类型获取函数:");
        logger.info("   GET {}/api/v1/expression/functions/type/{{type}}", baseUrl);
        logger.info("   curl {}/api/v1/expression/functions/type/custom", baseUrl);
        logger.info("   curl {}/api/v1/expression/functions/type/builtin", baseUrl);
        logger.info("");
        
        logger.info("4. 健康检查:");
        logger.info("   GET {}/api/v1/expression/health", baseUrl);
        logger.info("   curl {}/api/v1/expression/health", baseUrl);
        logger.info("");
        
        logger.info("5. 错误测试:");
        logger.info("   curl {}/api/v1/expression/functions/nonexistent", baseUrl);
        logger.info("   curl {}/api/v1/expression/functions/type/invalid", baseUrl);
        logger.info("");

        logger.info(StringUtils.repeat("=", 80));
        logger.info("💡 提示:");
        logger.info("- 在浏览器中访问上述 URL 可以查看 JSON 响应");
        logger.info("- 使用 curl 命令可以在命令行中测试 API");
        logger.info("- 按 Ctrl+C 停止服务器");
        logger.info(StringUtils.repeat("=", 80));
        logger.info("");
    }
}
