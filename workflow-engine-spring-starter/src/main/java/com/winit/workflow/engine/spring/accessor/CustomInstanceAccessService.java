package com.winit.workflow.engine.spring.accessor;

import com.winit.workflow.engine.core.api.InstanceResolver;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 自定义的bean反射
 */
@Component
public class CustomInstanceAccessService implements InstanceResolver, ApplicationContextAware {
    private ApplicationContext applicationContext;


    @Override
    public Object resolve(String name) {
        return applicationContext.getBean(name);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
