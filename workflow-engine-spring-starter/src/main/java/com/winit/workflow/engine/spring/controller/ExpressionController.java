package com.winit.workflow.engine.spring.controller;

import com.winit.workflow.engine.core.expression.ExpressionService;
import com.winit.workflow.engine.core.expression.FunctionInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Expression REST API Controller
 * Provides REST endpoints for expression-related operations
 */
@RestController
@RequestMapping("/api/v1/expression")
@CrossOrigin(origins = "*", maxAge = 3600)
public class ExpressionController {
    
    private static final Logger logger = LoggerFactory.getLogger(ExpressionController.class);
    
    @Autowired
    private ExpressionService expressionService;
    
    /**
     * Get all available expression functions
     * 
     * @return List of FunctionInfo containing all available functions
     */
    @GetMapping("/functions")
    public ResponseEntity<List<FunctionInfo>> getAllFunctions() {
        try {
            logger.info("Received request to get all expression functions");
            
            List<FunctionInfo> functions = expressionService.getAllFunctions();
            
            logger.info("Successfully retrieved {} expression functions", functions.size());
            
            return ResponseEntity.ok()
                    .header("Content-Type", "application/json")
                    .body(functions);
                    
        } catch (Exception e) {
            logger.error("Error retrieving expression functions", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * Get function by name
     * 
     * @param functionName the name of the function to retrieve
     * @return FunctionInfo for the specified function
     */
    @GetMapping("/functions/{functionName}")
    public ResponseEntity<FunctionInfo> getFunctionByName(@PathVariable String functionName) {
        try {
            logger.info("Received request to get function: {}", functionName);
            
            List<FunctionInfo> functions = expressionService.getAllFunctions();
            FunctionInfo function = functions.stream()
                    .filter(f -> f.getFunctionName().equals(functionName))
                    .findFirst()
                    .orElse(null);
            
            if (function != null) {
                logger.info("Successfully retrieved function: {}", functionName);
                return ResponseEntity.ok()
                        .header("Content-Type", "application/json")
                        .body(function);
            } else {
                logger.warn("Function not found: {}", functionName);
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            logger.error("Error retrieving function: {}", functionName, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * Get functions by type (builtin or custom)
     * 
     * @param type the type of functions to retrieve (builtin or custom)
     * @return List of FunctionInfo for the specified type
     */
    @GetMapping("/functions/type/{type}")
    public ResponseEntity<List<FunctionInfo>> getFunctionsByType(@PathVariable String type) {
        try {
            logger.info("Received request to get functions by type: {}", type);
            
            if (!"builtin".equals(type) && !"custom".equals(type)) {
                logger.warn("Invalid function type: {}", type);
                return ResponseEntity.badRequest().build();
            }
            
            List<FunctionInfo> functions = expressionService.getAllFunctions();
            List<FunctionInfo> filteredFunctions = functions.stream()
                    .filter(f -> f.getType().equals(type))
                    .collect(Collectors.toList());
            
            logger.info("Successfully retrieved {} functions of type: {}", filteredFunctions.size(), type);
            
            return ResponseEntity.ok()
                    .header("Content-Type", "application/json")
                    .body(filteredFunctions);
                    
        } catch (Exception e) {
            logger.error("Error retrieving functions by type: {}", type, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * Health check endpoint for expression service
     * 
     * @return Simple health status
     */
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        try {
            List<FunctionInfo> functions = expressionService.getAllFunctions();
            return ResponseEntity.ok("Expression service is healthy. " + functions.size() + " functions available.");
        } catch (Exception e) {
            logger.error("Expression service health check failed", e);
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                    .body("Expression service is not available");
        }
    }
}
