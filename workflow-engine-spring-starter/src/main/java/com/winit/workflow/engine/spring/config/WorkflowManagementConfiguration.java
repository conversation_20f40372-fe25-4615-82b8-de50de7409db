package com.winit.workflow.engine.spring.config;

import com.winit.workflow.engine.spring.properties.WorkflowManagementProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

/**
 * <AUTHOR>
 * 工作流管理端配置
 */
@ConditionalOnProperty(
        name = {"workflow.engine.management"},
        matchIfMissing = true
)
@EnableConfigurationProperties(WorkflowManagementProperties.class)
public class WorkflowManagementConfiguration {
}
