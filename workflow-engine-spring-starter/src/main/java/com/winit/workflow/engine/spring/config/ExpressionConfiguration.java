package com.winit.workflow.engine.spring.config;

import com.winit.workflow.engine.core.expression.DefaultExpressionService;
import com.winit.workflow.engine.core.expression.ExpressionEngine;
import com.winit.workflow.engine.core.expression.ExpressionService;
import com.winit.workflow.engine.core.expression.provider.MvelExpressionEngine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * Expression service configuration for Spring Boot
 */
@Configuration
@ComponentScan(basePackages = "com.winit.workflow.engine.spring.controller")
public class ExpressionConfiguration {
    
    private static final Logger logger = LoggerFactory.getLogger(ExpressionConfiguration.class);
    
    /**
     * Create ExpressionEngine bean if not already present
     */
    @Bean
    @ConditionalOnMissingBean(ExpressionEngine.class)
    public ExpressionEngine expressionEngine() {
        logger.info("Creating MvelExpressionEngine bean");
        return new MvelExpressionEngine();
    }
    
    /**
     * Create ExpressionService bean if not already present
     */
    @Bean
    @ConditionalOnMissingBean(ExpressionService.class)
    public ExpressionService expressionService(ExpressionEngine expressionEngine) {
        logger.info("Creating DefaultExpressionService bean with engine: {}", 
                expressionEngine.getClass().getSimpleName());
        return new DefaultExpressionService(expressionEngine);
    }
}
