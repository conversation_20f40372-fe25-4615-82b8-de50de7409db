package com.winit.workflow.engine.spring.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 * 工作流引擎初始化
 */
@Configuration
@ConfigurationProperties(prefix = "workflow-engine")
@DependsOn("workflowEngine")
@Import({WorkflowManagementConfiguration.class, WorkflowSmartEngineConfiguration.class, ExpressionConfiguration.class})
public class WorkflowEngineConfiguration {

}
