package com.winit.workflow.engine.spring.config;


import com.alibaba.smart.framework.engine.SmartEngine;
import com.alibaba.smart.framework.engine.configuration.IdGenerator;
import com.alibaba.smart.framework.engine.configuration.InstanceAccessor;
import com.alibaba.smart.framework.engine.configuration.ProcessEngineConfiguration;
import com.alibaba.smart.framework.engine.configuration.impl.DefaultProcessEngineConfiguration;
import com.alibaba.smart.framework.engine.extension.scanner.SimpleAnnotationScanner;
import com.alibaba.smart.framework.engine.model.instance.Instance;
import com.winit.workflow.engine.core.api.InstanceResolver;
import com.winit.workflow.engine.core.api.WorkflowEngine;
import com.winit.workflow.engine.core.provider.smartengine.SmartEngineConfig;
import com.winit.workflow.engine.core.provider.smartengine.SmartEngineInstanceAccessor;
import com.winit.workflow.engine.core.spi.SpiProviderFactory;
import com.winit.workflow.engine.core.spi.WorkflowEngineProvider;
import com.winit.workflow.engine.sdk.service.FlowRuntimeService;
import com.winit.workflow.engine.sdk.service.impl.FlowRuntimeServiceImpl;
import com.winit.workflow.engine.spring.accessor.CustomInstanceAccessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.atomic.AtomicLong;

@Configuration
public class WorkflowSmartEngineConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public InstanceResolver instanceResolver() {
        return new CustomInstanceAccessService();
    }

    @Bean
    @ConditionalOnMissingBean
    public InstanceAccessor smartEngineInstanceAccessor(InstanceResolver instanceResolver) {
        return new SmartEngineInstanceAccessor(instanceResolver);
    }

    @Bean
    public SmartEngineConfiguration smartEngineConfigHelper(InstanceAccessor smartEngineInstanceAccessor) {
        return new SmartEngineConfiguration(smartEngineInstanceAccessor);
    }

    @Bean
    @ConditionalOnMissingBean
    public WorkflowEngine workflowEngine(SmartEngineConfiguration smartEngineConfiguration) {
        // 使用SPI机制获取WorkflowEngineProvider
        WorkflowEngineProvider provider = SpiProviderFactory.getProvider();
        return provider.createEngine(smartEngineConfiguration);
    }

    @Bean
    public FlowRuntimeService flowRuntimeService(WorkflowEngine workflowEngine) {
        return new FlowRuntimeServiceImpl(workflowEngine);
    }


    /**
     * SmartEngine配置
     */
    public static class SmartEngineConfiguration implements SmartEngineConfig {

        private final InstanceAccessor smartEngineInstanceAccessor;

        public SmartEngineConfiguration(@Autowired InstanceAccessor smartEngineInstanceAccessor) {
            this.smartEngineInstanceAccessor = smartEngineInstanceAccessor;
        }

        @Override
        public ProcessEngineConfiguration initWorkflowEngineConfig() {

            DefaultProcessEngineConfiguration defaultProcessEngineConfiguration = new DefaultProcessEngineConfiguration();

            defaultProcessEngineConfiguration.setInstanceAccessor(smartEngineInstanceAccessor);
            defaultProcessEngineConfiguration.setIdGenerator(new TimeBasedIdGenerator());
            defaultProcessEngineConfiguration.setAnnotationScanner(new SimpleAnnotationScanner(
                    SmartEngine.class.getPackage().getName(),
                    "com.winit.workflow.agent"
            ));


            return defaultProcessEngineConfiguration;
        }

        // 如果编译器要求这个方法，我们添加一个默认实现
        public InstanceResolver instanceResolver() {
            // 这个方法可能在某些版本的接口中是必需的
            // 返回 null 或者抛出异常，因为我们通过构造函数注入 InstanceAccessor
            throw new UnsupportedOperationException("InstanceResolver is provided through constructor injection");
        }
    }

    /**
     * 时间戳ID生成器
     */
    public static class TimeBasedIdGenerator implements IdGenerator {

        private final AtomicLong temp = new AtomicLong(0);

        @Override
        public void generate(Instance instance) {
            String s = System.currentTimeMillis() + temp.getAndIncrement() + "";
            instance.setInstanceId(s);
        }
    }


}
