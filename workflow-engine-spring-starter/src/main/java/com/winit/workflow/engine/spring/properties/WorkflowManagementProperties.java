package com.winit.workflow.engine.spring.properties;


import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(
        prefix = "workflow.engine.management"
)
public class WorkflowManagementProperties {

    /**
     * 是否启用工作流管理平台管理工作流
     */
    private boolean enabled = false;
    /**
     * 服务端地址
     */
    private String baseUrl;

    /**
     * 读取超时时间
     */
    private int readTimeout;

    /**
     * 连接超时时间
     */
    private int connectTimeout;


    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}
